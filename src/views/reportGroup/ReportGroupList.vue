<template>
  <div class="categories-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Report Groups"
      add-label="Report Group"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
      hide-import-export
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No report groups found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
                >{{ item.name }}</span
              >
            </template>

            <template #item.department="{ item }">
              <span>{{ item.department.join(", ") }}</span>
            </template>
            <template #item.category="{ item }">
              <span>{{ item.category.map((c) => c.name).join(", ") }}</span>
            </template>
            <template #item.workarea="{ item }">
              <span>{{ item.workarea.map((w) => w.name).join(", ") }}</span>
            </template>

            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Report Group"
                :id="item.id"
                :name="item.name"
                activate-url="/report-groups/:id/activate"
                deactivate-url="/report-groups/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useReportGroupStore } from "@/stores/reportGroup";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";

const reportGroupStore = useReportGroupStore();
const router = useRouter();

const search = ref(null);
const loading = ref(false);

const items = computed(() => reportGroupStore.getReportGroups || []);
const headers = ref(tableHeaders["reportGroup"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const selectedStatus = ref(null);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(
      (item) => item.activeStatus === selectedStatus.value
    );
  }
  return result;
});

const selectStatus = (status) => {
  selectedStatus.value = status ?? null;
};

const applyFilters = (filters) => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setReportGroup"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

const add = () => {
  router.push({ name: "create-report-group" });
};

const edit = (id) => {
  router.push({ name: "edit-report-group", params: { id } });
};

const handleSearch = (v) => {
  search.value = v;
};

const fetch = async () => {
  await reportGroupStore.fetchReportGroups();
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped></style>
