<template>
  <form-actions-bar
    @close="navigatePrevious"
    @submit="save"
    :loading="isLoading"
  />
  <v-container v-if="!loader">
    <v-card-text class="pt-4">
      <v-form ref="form">
        <v-row>
          <v-col cols="6">
            <v-text-field
              v-model="record.name"
              label="Name"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              @blur="cleanName"
              :rules="[rules.require]"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              v-model="record.department"
              label="Department"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              @blur="cleanName"
              :rules="[rules.require]"
            />
          </v-col>
          <v-col cols="6">
            <category-field
              v-model="record.category"
              return-object
              multiple
              :rules="[rules.require]"
            />
          </v-col>
          <v-col cols="6">
            <work-area-field
              v-model="record.workarea"
              return-object
              multiple
              :rules="[rules.require]"
            />
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { reportGroupRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useReportGroupStore } from "@/stores/reportGroup";
import { formatName } from "@/helpers/formatter";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import CategoryField from "@/components/fields/CategoryField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";

const route = useRoute();
const router = useRouter();
const reportGroupStore = useReportGroupStore();
const reportGroupId = route.params.id;
const isEdit = reportGroupId !== undefined;

const isLoading = ref(false);
const form = ref(null);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const loader = ref(false);

onBeforeMount(async () => {
  if (isEdit) {
    loader.value = true;

    try {
      const result = await reportGroupStore.fetchReportGroupById(reportGroupId);
      record.value = { ...result, department: result.department.join(", ") };
      loader.value = false;
    } catch (error) {
      console.log(error);
    }
  }
});

const save = async () => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      isLoading.value = false;
      return;
    }
    const payload = {
      ...record.value,
      department: record.value.department.split(","),
      workarea: record.value.workarea.map((w) => {
        return {
          id: w.id,
          name: w.name
        };
      })
    };

    if (isEdit) await reportGroupStore.updateReportGroup(payload);
    else await reportGroupStore.createReportGroup(payload);
    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const navigatePrevious = () => {
  router.push({ name: "report-groups" });
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
};
</script>
