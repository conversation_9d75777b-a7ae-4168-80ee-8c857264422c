<template>
  <v-dialog v-model="model" max-width="500" persistent>
    <v-card class="pa-4">
      <v-card-title class="text-h6 text-center">
        GRN Created Successfully 🎉
      </v-card-title>

      <v-divider class="my-3"></v-divider>

      <v-card-text>
        <v-row dense>
          <v-col cols="6" class="text-subtitle-2 text-grey">GRN Number</v-col>
          <v-col cols="6" class="text-right font-weight-medium">
            {{ grn.grnNumber }}
          </v-col>

          <v-col cols="6" class="text-subtitle-2 text-grey"
            >Invoice Number</v-col
          >
          <v-col cols="6" class="text-right font-weight-medium">
            {{ grn.invoiceNumber }}
          </v-col>

          <v-col cols="6" class="text-subtitle-2 text-grey">Vendor</v-col>
          <v-col cols="6" class="text-right font-weight-medium">
            {{ grn.vendorName }}
          </v-col>

          <v-col cols="6" class="text-subtitle-2 text-grey">Location</v-col>
          <v-col cols="6" class="text-right font-weight-medium">
            {{ grn.location.name }}
          </v-col>

          <v-col cols="6" class="text-subtitle-2 text-grey">Workarea</v-col>
          <v-col cols="6" class="text-right font-weight-medium">
            {{ grn.inventoryLocation.name }}
          </v-col>

          <v-col cols="6" class="text-subtitle-2 text-grey">Total Amount</v-col>
          <v-col cols="6" class="text-right font-weight-medium text-success">
            {{ grn.totalAmount.toFixed(2) }}
          </v-col>
        </v-row>
      </v-card-text>

      <v-divider class="my-2"></v-divider>

      <v-card-actions class="justify-end">
        <v-btn variant="text" color="grey" @click="closeDialog">Close</v-btn>
        <v-btn color="primary" @click="viewDetails">
          View Details
          <v-icon end>mdi-open-in-new</v-icon>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
const props = defineProps({
  grn: { type: Object, required: true },
});

const emit = defineEmits(["close", "view-details"]);

const model = defineModel();

const closeDialog = () => {
  model.value = false;
  emit("close");
};

const viewDetails = () => {
  emit("view-details", props.grn.id);
  model.value = false;
};
</script>
