<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'goods-received-notes' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ record.grnNumber }}</template>

      <template #actions>
        <GRNOptions :status="record.status" :itemId="grnId" @refresh="get" />
      </template>
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-else fluid>
      <!-- Overview -->
      <GRNOverview :data="record" @update="get" />

      <!-- Item Table -->
      <GRNTableItems :items="tableItems" :record="record" />
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";

import { useGrnStore } from "@/stores/goodsReceivedNote";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import GRNOptions from "@/components/purchase/GRNOptions.vue";
import GRNOverview from "@/components/purchase/GRNOverview.vue";
import GRNTableItems from "@/components/purchase/viewTable/GRNTableItems.vue";

const grnStore = useGrnStore();
const route = useRoute();

const grnId = route.params.id;
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);

const get = async () => {
  loading.value = true;
  try {
    const { items, ...result } = await grnStore.fetchGrnById(grnId);
    record.value = result;
    tableItems.value = items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
