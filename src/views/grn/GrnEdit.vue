<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Update GRN"
          :loading="loading"
          :disabled="loading"
          @click="submit"
          variant="flat"
          color="primary"
          class="ml-2"
        />
      </template>
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-form ref="form">
        <v-row class="mb-1">
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="cart.grnDate"
              label="Goods Received Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="cart.invoiceDate"
              label="Vendor Invoice Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="selectedLocation"
              label="Add Location for direct indent"
              multiple
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              v-model="selectedWorkArea"
              label="Add workarea for direct indent"
              :locations="selectedLocation"
              :multiple="true"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              class="required-fld"
              v-model="cart.invoiceNumber"
              label="Invoice No"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
            ></v-text-field>
          </v-col>
        </v-row>
      </v-form>

      <grn-overview :data="cart" />

      <grn-table
        :cart="cart"
        :grn-list="tableItems"
        @edit="modifyItem"
        @calculate="calculate"
        @copy="duplicateItem"
        @removeItem="removeItem"
      ></grn-table>

      <!-- Drawer Form -->
      <PRItemForm
        v-model="openNav"
        @add="addItem"
        :locationId="selectedLocation[0]"
        :inventoryLocationId="selectedWorkArea[0]"
        :selectedVendorId="cart.vendor.id"
        :currentSelectedItem="selectedItem"
        :showAdd="false"
      />
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import rules from "@/helpers/rules";
import { useRoute, useRouter } from "vue-router";

// components
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import cartHelper from "@/helpers/cartHelper";
import GrnTable from "@/components/purchase/GrnTable.vue";
import GrnOverview from "@/components/purchase/GRNOverview.vue";
import PRItemForm from "@/components/purchase/PRItemForm.vue";
import { useMasterStore } from "@/stores/masterStore";

const grnStore = useGrnStore();
const masterStore = useMasterStore();

const loading = ref(false);
const cart = ref(cartHelper.NewCart("po"));
const tableItems = ref([]);
const openNav = ref(false);

const router = useRouter();
const route = useRoute();
const grnId = route.params.id;

const selectedLocation = ref([]);
const selectedWorkArea = ref([]);
const selectedItem = ref(null);

const itemOrdinal = ref(0);

const otherCharges = computed(() => {
  return masterStore.getCharges();
});

const navigatePrevious = () => {
  router.push({ name: "goods-received-notes" });
};

const addItem = (item) => {
  // handle item form close and reset
  selectedItem.value = null;
  openNav.value = false;

  itemOrdinal.value++;

  cart.value = cartHelper.AddItem(cart.value, {
    ...item,
    receivedQty: item.quantity,
    ordinal: itemOrdinal.value,
  });
  tableItems.value = cart.value.items;
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

const calculate = (charges, taxes) => {
  cart.value = cartHelper.Calculate({ ...cart.value, charges, taxes });
};

const removeItem = (index) => {
  cart.value = cartHelper.RemoveItem(cart.value, index);
  tableItems.value = cart.value.items;
};

const duplicateItem = (item) => {
  selectedItem.value = item;
  openNav.value = true;
};

// const checkSubmit = async () => {
//   try {
//     if (loading.value) return;

//     const { valid } = await form.value.validate();
//     if (!valid) return;

//     const isAllQuantityMatched = tableItems.value.every(
//       (item) => item.orderedQty <= item.receivedQty
//     );

//     loading.value = true;

//     if (isAllQuantityMatched) {
//       await submit(3);
//     } else {
//       dialog.value = true;
//     }
//   } finally {
//     loading.value = false;
//   }
// };

const submit = async () => {
  loading.value = true;

  const { createdAt, deliveryDate, ...payload } = { ...cart.value };

  await grnStore.updateGRN(payload);
  loading.value = false;
};

onBeforeMount(async () => {
  loading.value = true;
  // @todo add dateRange

  try {
    const result = await grnStore.fetchGrnById(grnId);
    cart.value = {
      ...cartHelper.NewCart("po"),
      ...result,
    };
    tableItems.value = result.items;
    itemOrdinal.value = tableItems.value[0].ordinal;
    selectedWorkArea.value = [cart.value.inventoryLocation.id];
    selectedLocation.value = [cart.value.location.id];
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>
