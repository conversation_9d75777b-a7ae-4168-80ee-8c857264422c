<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Return To Vendor"
          :loading="loading"
          :disabled="loading"
          @click="submit"
          variant="flat"
          color="primary"
          class="ml-2"
        />
      </template>
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-form ref="form">
        <v-row class="mb-1">
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              v-model="record.creditNoteNo"
              label="Credit Note No."
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="record.creditNoteDate"
              label="Credit Note Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="record.returnDate"
              label="Return Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-textarea
              v-model="record.remarks"
              label="Remarks"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              rows="1"
              auto-grow
            ></v-textarea>
          </v-col>
        </v-row>
      </v-form>

      <grn-overview :data="cart" />

      <RTVTable
        :cart="cart"
        :grn-list="tableItems"
        @edit="modifyItem"
        @calculate="calculate"
      ></RTVTable>
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import rules from "@/helpers/rules";
import { useRoute, useRouter } from "vue-router";

// components
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import cartHelper from "@/helpers/cartHelper";
import RTVTable from "@/components/purchase/RTVTable.vue";
import GrnOverview from "@/components/purchase/GRNOverview.vue";

const grnStore = useGrnStore();

const loading = ref(false);
const record = ref({
  creditNoteNo: null,
  creditNoteDate: new Date(),
  returnDate: new Date(),
  remarks: null,
});
const cart = ref(cartHelper.NewCart("rtv"));
const tableItems = ref([]);

const router = useRouter();
const route = useRoute();
const grnId = route.params.id;
const form = ref(null);

const itemOrdinal = ref(0);

const navigatePrevious = () => {
  router.push({ name: "goods-received-notes" });
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

const calculate = (charges, taxes) => {
  cart.value = cartHelper.Calculate({ ...cart.value, charges, taxes });
};

const submit = async () => {
  try {
    if (loading.value) return;
    loading.value = true;

    const { valid, errors } = await form.value.validate();
    if (!valid) {
      console.log(errors);
      return;
    }
    const payload = {
      ...record.value,
      items: tableItems.value,
    };
    await grnStore.returnGRN(cart.value.id, payload);
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    const result = await grnStore.fetchGrnById(grnId);
    const items = result.items.map((item) => ({
      ...item,
      rtvQty: item.receivedQty,
      remarks: null,
    }));
    cart.value = {
      ...cartHelper.NewCart("rtv"),
      ...result,
      items,
    };
    tableItems.value = items.map((item) =>
      cartHelper.CalculateItem(cart.value, item)
    );
    itemOrdinal.value = tableItems.value[0].ordinal;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>
