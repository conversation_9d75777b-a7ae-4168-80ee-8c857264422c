<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <p>
          {{ isEdit ? "Edit Ledger" : "Create Ledger" }}
        </p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4" style="padding: 15px">
        <v-form ref="form">
          <v-text-field
            class="required-fld"
            v-model.trim="record.name"
            label="Enter Ledger name"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"
            @keyup.enter="submit(true)"
          />
        </v-form>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex flex-column mx-2 mb-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";

const props = defineProps({
  isEdit: Boolean,
  ledgerData: Object,
});

const model = defineModel();
const emit = defineEmits(["create", "update"]);

const form = ref(null);

const record = ref({
  name: null,
});

const resetForm = () => {
  record.value = { name: null };
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
};

watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.ledgerData) {
    record.value = { ...props.ledgerData };
  } else {
    resetForm();
  }
});

const loadBtn = ref(null);

const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = { ...record.value };

  const done = (success) => {
    loadBtn.value = null;
    if (!success) return;
    if (closeAfter) {
      model.value = false;
    } else {
      resetForm();
    }
  };

  if (props.isEdit) {
    emit("update", payload, done);
  } else {
    emit("create", payload, done);
  }
};

const closeDialog = () => {
  model.value = false;
};
</script>
