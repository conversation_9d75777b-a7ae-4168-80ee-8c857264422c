<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Ledger"
      add-label="Ledger"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      :hide-import-export="true"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No Ledgers found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
                >{{ item.name }}</span
              >
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Ledger"
                :name="item.name"
                :id="item.id"
                activate-url="/ledgers/:id/activate"
                deactivate-url="/ledgers/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <ProductLedgerForm
      v-model="dialog"
      :isEdit="isEdit"
      :ledgerData="editLedgerData"
      @create="handleCreateLedger"
      @update="handleUpdateLedger"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { tableHeaders } from "@/helpers/tableHeaders";
import { useProductLedgerStore } from "@/stores/productLedger";
import { filterData } from "@/helpers/searchFilter";
import ProductLedgerForm from "@/views/productLedgers/ProductLedgerForm.vue";

const ledgerStore = useProductLedgerStore();

const search = ref("");
const loading = ref(false);

const headers = ref(tableHeaders["ledgers"]);
const sortBy = ref([{ key: "name", order: "asc" }]);
const dialog = ref(false);
const isEdit = ref(false);

const selectedStatus = ref(null);

const selectStatus = (status) => {
  selectedStatus.value = status ?? null;
};

const items = computed(() => ledgerStore.getProductLedgers || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(
      (item) => item.activeStatus === selectedStatus.value
    );
  }
  return result;
});

const applyFilters = (filters) => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders.ledgers(filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

const handleSearch = (query) => {
  search.value = query;
};

const editLedgerData = ref(null);

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editLedgerData.value = null;
};

const edit = async (id) => {
  try {
    const ledger = await ledgerStore.fetchProductLedgerById(id);
    editLedgerData.value = ledger;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error(error);
  }
};

const handleCreateLedger = async (ledger, done) => {
  try {
    await ledgerStore.createProductLedger(ledger);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const handleUpdateLedger = async (ledger, done) => {
  try {
    await ledgerStore.updateProductLedger(ledger);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const fetch = async () => {
  await ledgerStore.fetchProductLedgers();
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  fetch();
});
</script>

<style scoped></style>
