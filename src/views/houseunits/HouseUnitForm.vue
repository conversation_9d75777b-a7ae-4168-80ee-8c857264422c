<template>
  <v-dialog v-model="dialog" max-width="800" persistent scrollable>
    <v-card style="height: 95vh; overflow-y: auto">
      <v-card-title>
        <v-row align="center" justify="space-between">
          <v-col cols="auto ">
            <span>{{ title }} House Unit</span>
          </v-col>
          <v-col cols="auto">
            <v-btn
              icon="mdi-close"
              variant="text"
              @click="dialog = false"
              color="error"
            ></v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-divider />

      <v-card-text class="pa-4">
        <v-form ref="form">
          <v-row>
            <v-col cols="6">
              <v-text-field
                class="required-fld"
                v-model.trim="record.name"
                label="Unit Name"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @blur="cleanName"
                :rules="[rules.require, rules.maxLength(100)]"
              ></v-text-field>
            </v-col>
            <v-col cols="6">
              <v-text-field
                class="required-fld"
                v-model="record.symbol"
                label="Unit Symbol"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :rules="[rules.require, rules.unitLength]"
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <p class="text-medium-emphasis">
                <v-icon icon="mdi-scale-balance"></v-icon>
                <span class="mx-2">Conversion</span>
              </p>
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model.number="record.quantity"
                label="Quantity"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                type="number"
                @keydown.up.prevent
                @keydown.down.prevent
                :rules="[rules.quantity, rules.positive]"
              ></v-text-field>
            </v-col>
            <v-col cols="6">
              <v-autocomplete
                v-model="record.toUnit"
                :items="availableUnits"
                item-title="name"
                item-value="symbol"
                label="To unit"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                clearable
              >
                <template v-slot:item="{ props, item }">
                  <v-list-item
                    v-bind="props"
                    :title="`${item.raw.name} (${item.raw.symbol})`"
                  ></v-list-item>
                </template>
                <template v-slot:selection="{ item }">
                  {{ item.raw.name }} ({{ item.raw.symbol }})
                </template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="d-flex justify-space-between">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2">
          <v-btn
            color="primary"
            variant="flat"
            @click="save(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEditing"
            color="primary"
            variant="flat"
            @click="save(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import rules from "@/helpers/rules";
import { useHouseUnitStore } from "@/stores/houseUnit";
import { formatName } from "@/helpers/formatter";

const dialog = defineModel();
const emit = defineEmits(["handleSave"]);
const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    required: true,
  },
  isEditing: Boolean,
  loading: { type: Boolean, default: false },
});

const form = ref(null);
const houseUnitStore = useHouseUnitStore();

const availableUnits = ref([]);
const loadBtn = ref(null);

const resetLoading = () => {
  loadBtn.value = null;
};

const resetForm = () => {
  form.value.reset(); // Vuetify form reset if using <v-form ref="form">
};

defineExpose({ resetLoading, resetForm });

// Load tenant-specific units for the dropdown without mutating global table data
const loadAvailableUnits = async () => {
  try {
    const list = await houseUnitStore.fetchHouseUnitsBytenantList();
    availableUnits.value = Array.isArray(list) ? list : [];
  } catch (error) {
    // handle silently
    availableUnits.value = [];
  }
};

watch(
  dialog,
  (isOpen) => {
    if (isOpen) {
      loadAvailableUnits();
    }
  },
  { immediate: true }
);

// Load once on mount in case dialog opens with delayed data
onMounted(() => {
  if (dialog?.value) {
    loadAvailableUnits();
  }
});

const save = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = {
    ...props.record,
  };

  emit("handleSave", payload, closeAfter);
};

const cleanName = () => {
  props.record.name = formatName(props.record.name);
};
</script>

<style scoped></style>
