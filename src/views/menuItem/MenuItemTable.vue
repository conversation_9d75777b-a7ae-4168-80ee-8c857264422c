<template>
  <div>
    <div class="d-flex align-end justify-space-between" style="padding: 16px">
      <h3>
        <v-icon icon="mdi-food" size="20" />
        <span class="mx-2" style="font-weight: 450; letter-spacing: 1px">
          {{ record.itemName?.toUpperCase() || "" }}
        </span>
      </h3>
      <div>
        <v-btn
          class="mx-3"
          @click="navigatePrevious"
          variant="outlined"
          color="primary"
        >
          <v-icon icon="mdi-close" />Close
        </v-btn>
        <v-btn text="Save" @click="onSave" variant="flat" color="primary" />
      </div>
    </div>

    <v-divider />

    <v-container fluid>
      <v-row>
        <v-card class="ma-3 mb-0" width="100%">
          <v-container fluid class="pa-0">
            <v-data-table
              :headers="menuItemIngredientHeaders"
              :items="flattenedItems"
              :loading="props.loader"
              items-per-page="-1"
              :no-data-text="loader ? 'Loading...' : 'No items'"
            >
              <template v-slot:item="{ item, index }">
                <tr>
                  <td>{{ item.itemName?.toUpperCase() }}</td>
                  <td class="py-4">
                    <v-autocomplete
                      class="required-fld"
                      v-model="item.selectedItemType"
                      :items="['Inventory', 'Recipe', 'No Consumption']"
                      :rules="[rules.require]"
                      density="compact"
                      variant="outlined"
                      label="Select Type"
                      single-line
                      hide-details
                      color="primary"
                      @update:modelValue="resetItem(item)"
                      clearable
                    />
                  </td>

                  <td class="py-4">
                    <div class="d-flex">
                      <v-autocomplete
                        class="required-fld"
                        v-model="item.selectedItemName"
                        :items="getFilteredItems(index, item.selectedItemType)"
                        item-title="name"
                        item-value="id"
                        :rules="[rules.require]"
                        density="compact"
                        variant="outlined"
                        label="Search Items"
                        single-line
                        hide-details
                        color="primary"
                        @update:modelValue="() => itemNameChange(item)"
                        :readonly="!item.selectedItemType"
                        :disabled="item.selectedItemType === 'No Consumption'"
                        clearable
                      />
                      <v-divider vertical class="mx-5" />
                      <v-tooltip location="top">
                        <template #activator="{ props }">
                          <v-btn
                            v-bind="props"
                            color="primary"
                            @click="add"
                            :disabled="
                              !item.selectedItemType ||
                              item.selectedItemType === 'No Consumption'
                            "
                          >
                            CREATE
                          </v-btn>
                        </template>
                        <span>{{
                          item.selectedItemType === "Inventory"
                            ? "Create Inventory"
                            : "Create Recipe"
                        }}</span>
                      </v-tooltip>
                    </div>
                  </td>

                  <td>
                    <div class="d-flex flex-column ga-2 mt-3 mb-3">
                      <div
                        v-for="(level, i) in item.servingLevels"
                        :key="i"
                        class="d-flex align-center"
                      >
                        <span
                          class="font-weight-medium"
                          style="min-width: 100px"
                          :style="{
                            opacity:
                              item.selectedItemType === 'No Consumption'
                                ? 0.5
                                : 1,
                          }"
                        >
                          {{ level.servingSizeName?.toUpperCase() || "SIZE" }}
                        </span>
                        <v-text-field
                          v-model="level.qty"
                          type="number"
                          density="compact"
                          variant="outlined"
                          single-line
                          hide-details
                          color="primary"
                          :readonly="
                            !item.selectedItemType || !item.selectedItemName
                          "
                          :disabled="item.selectedItemType === 'No Consumption'"
                        >
                          <template v-slot:append-inner>
                            {{
                              level.recipeUnit || item.recipeUnit?.symbol || ""
                            }}
                          </template>
                        </v-text-field>
                      </div>
                    </div>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-container>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import rules from "@/helpers/rules";
import { menuItemIngredientHeaders } from "@/helpers/tableHeaders";
import { useMenuItemStore } from "@/stores/menuItem";

const props = defineProps({
  record: Object,
  flattenedItems: Array,
  inventoryList: Array,
  recipeList: Array,
  saveHandler: Function,
  routeName: String,
  loader: Boolean,
});

const route = useRoute();

const { updateRecipeUnit } = useMenuItemStore();
const loader = ref(false);
const $confirm = inject("confirm");

const router = useRouter();

const inventoryItemNames = computed(() =>
  props.inventoryList.map((i) => ({ id: i.id, name: i.itemName }))
);
const recipeNames = computed(() =>
  props.recipeList.map((r) => ({ id: r.id, name: r.name }))
);
const selectedItemNames = computed(() =>
  props.flattenedItems.map((i) => i.selectedItemName).filter(Boolean)
);

const getFilteredItems = (index, type) => {
  const allItems =
    type?.toLowerCase() === "recipe"
      ? recipeNames.value
      : inventoryItemNames.value;
  const selected = selectedItemNames.value;
  const current = props.flattenedItems[index]?.selectedItemName;
  return allItems.filter(
    (item) => !selected.includes(item) || item === current
  );
};

const resetItem = (item) => {
  item.selectedItemName = null;
  item.recipeUnit = null;
  item.servingLevels?.forEach((level) => {
    level.qty = null;
    level.recipeUnit = null;
  });
};

const itemNameChange = (item) => {
  updateRecipeUnit(item, props.inventoryList, props.recipeList);
  item.servingLevels?.forEach((level) => {
    level.qty = null;
  });
};

const createDialogMessage = computed(() => {
  const type = props.flattenedItems[0]?.selectedItemType;
  switch (type) {
    case "Inventory":
      return "Are you sure you want to Create Inventory Item?";
    case "Recipe":
      return "Are you sure you want to Create Recipe?";
    default:
      return "Are you sure you want to create a new item?";
  }
});

const add = async() => {
  const type = props.flattenedItems[0]?.selectedItemType;
  if (!type) return;

  if (!$confirm) {
      console.error("Global confirm dialog not available");
      return;
  }

  const ok = await $confirm(createDialogMessage);
  if (!ok) {
      return
  }

  const target =
    type === "Inventory" ? "create-inventory-item" : "create-recipe";
  const query =
    props.routeName === "menu-items"
      ? {
          menuItemType: type,
          menuItemId: props.record.id,
          accountId: props.record.account.id,
        }
      : {
          modifierType: type,
          modifierId: props.record.id,
          accountId: props.record.account.id,
        };

  router.push({ name: target, query });
};

const navigatePrevious = () => router.push({ name: props.routeName });

const onSave = () => {
  props.saveHandler();
};

watch(
  () => props.flattenedItems,
  (items) => {
    if (!props.record?.linkingStatus || items.length === 0) return;

    const createdItemId = route.query.createdItemId;
    const createdItemType = route.query.createdItemType;
    const isNewItemLinked = !!(createdItemId && createdItemType);

    items.forEach((item) => {
      item.selectedItemType = createdItemType ?? props.record.itemType;
      item.selectedItemName = createdItemId ?? props.record.item;

      if (!isNewItemLinked) {
        item.servingLevels?.forEach((level) => {
          const matched = props.record.servingLevels?.find(
            (sl) => sl.servingSizeId === level.servingSizeId
          );
          if (matched) {
            level.qty = matched.qty;
            level.recipeUnit = matched.recipeUnit;
          }
        });
      }
    });
  }
);
</script>
