<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'spoilage' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ record.spoilageNumber }}</template>
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-else fluid>
      <!-- Overview -->
      <SpoilageOverview :data="record" />

      <!-- Item Table -->
      <SpoilageTableItems :items="tableItems" :record="record" />
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";
import { useSpoilageStore } from "@/stores/spoilage";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import SpoilageTableItems from "./SpoilageTableItems.vue";
import SpoilageOverview from "./SpoilageOverview.vue";

const spoilageStore = useSpoilageStore();
const route = useRoute();

const spoilageId = route.params.id;
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);

const get = async () => {
  loading.value = true;
  try {
    const { items, ...result } = await spoilageStore.fetchSpoilageById(spoilageId);
    record.value = result;
    tableItems.value = items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
