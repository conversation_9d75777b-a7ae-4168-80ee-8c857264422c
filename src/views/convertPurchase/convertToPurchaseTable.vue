<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="itemsList"
          :headers="headers"
          items-per-page="-1"
          hide-default-footer
        >
          <template #header.index="{ props }">
            <v-checkbox
              v-model="selectAll"
              color="primary"
              density="compact"
              hide-details
              @change="toggleSelectAll"
            ></v-checkbox>
          </template>

          <!-- <template #item.index="{ index }">{{ index + 1 }}</template> -->

          <template #item.index="{ item }">
            <v-checkbox
              v-model="item.selected"
              color="primary"
              density="compact"
              hide-details
              @change="$emit('edit', item)"
            ></v-checkbox>
          </template>

          <!-- <template #item.actions="{ index }">
            <v-icon
              color="error"
              @click="$emit('removeItem', index)"
              @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template> -->

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.vendor="{ item, index }">
            <v-autocomplete
              v-model="item.vendor"
              :items="item.vendorDetails"
              item-title="vendorName"
              item-value="vendorId"
              label="Vendor"
              variant="outlined"
              density="compact"
              color="primary"
              clearable
              hide-details="auto"
              return-object
              @update:model-value="
                (val) => handleVendorUpdate(val, item, index)
              "
            />
          </template>

          <template #item.quantity="{ item, index }">
            <v-text-field
              v-model.number="item.quantity"
              type="number"
              min="1"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'quantity')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.unitCost="{ item, index }">
            <v-text-field
              v-model.number="item.unitCost"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              :readonly="item.contract"
              persistent-hint
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'unitCost')"
              @update:model-value="$emit('edit', item, index)"
              min-width="100px"
            >
              <!-- indicate contract -->
              <template #prepend-inner v-if="item.contract">
                <v-icon size="small" icon="mdi-file-sign" color="purple" />
              </template>
            </v-text-field>
          </template>

          <template #item.totalDiscount="{ item, index }">
            <v-text-field
              v-model.number="item.totalDiscount"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalDiscount')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.totalCess="{ item, index }">
            <v-text-field
              v-model.number="item.totalCess"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalCess')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.taxRate="{ item, index }">
            <tax-field
              v-model="item.taxes"
              multiple
              return-object
              :hint="`Total: ${String(selectedTaxAmount(item))}%`"
              persistent-hint
              @update:model-value="$emit('edit', item, index)"
            ></tax-field>
          </template>
          <template #item.netAmount="{ item }">{{
            truncateNumber(item.netAmount)
          }}</template>

          <template #item.totalTaxAmount="{ item }">{{
            truncateNumber(item.totalTaxAmount)
          }}</template>

          <template #item.totalAmount="{ item }">{{
            truncateNumber(item.totalAmount)
          }}</template>

          <!-- 👇 Aligned Footer Totals -->
          <template #tfoot>
            <tr class="sticky-bottom-row">
              <!-- index -->
              <td></td>
              <!-- Item Name -->
              <td class="font-weight-bold">Total</td>
              <!-- vendor -->
              <td></td>
              <!-- stock -->
              <td></td>
              <!-- quantity -->
              <td></td>
              <!-- UOM -->
              <td></td>
              <!-- unit cost -->
              <td></td>
              <!-- Discount amt -->
              <td></td>
              <!-- Cess -->
              <td></td>
              <!-- Tax Rate -->
              <td></td>
              <!-- Exclusive Tax Total -->
              <td class="text-end font-weight-bold pr-4">
                {{ truncateNumber(cart.netAmount) }}
              </td>
              <td class="text-end font-weight-bold pr-4">
                {{ truncateNumber(cart.totalTaxAmount) }}
              </td>
              <!-- Inclusive Tax Total -->
              <td class="text-end font-weight-bold pr-4">
                {{ truncateNumber(cart.totalAmount) }}
              </td>
              <!-- <td></td>. -->
            </tr>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref } from "vue";
import { truncateNumber } from "@/helpers/money";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import TaxField from "@/components/fields/TaxField.vue";

const props = defineProps({
  itemsList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const handleVendorUpdate = (val, item, index) => {
  item.unitCost = val?.price ? val.price : item.cost;
  item.contract = !!val?.contractType;

  emit("edit", item, index);
};

const selectAll = ref(true);

const toggleSelectAll = () => {
  props.itemsList.forEach((item) => {
    item.selected = selectAll.value;
  });
};

const selectedTaxAmount = (item) => {
  return item.taxes.reduce((acc, tax) => {
    return acc + tax.value;
  }, 0);
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "quantity") {
    if (item.quantity < 1 || !item.quantity) {
      item.quantity = 1;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: discount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost * item.quantity) {
    item.totalDiscount = item.unitCost * item.quantity;
  }
};

onBeforeMount(() => {
  props.itemsList.forEach((item, index) => {
    item.cost = item.unitCost;
    if (item.vendorDetails.length > 0) {
      const vendor = item.vendorDetails[0];
      item.vendor = {
        vendorId: vendor.vendorId,
        vendorName: vendor.vendorName,
        price: vendor?.price,
      };

      item.unitCost = vendor?.price ?? item.cost;
      item.contract = !!vendor?.contractType;

      emit("edit", item, index);
    }
    item.selected = item.selected ?? true;
  });
});

const emit = defineEmits(["removeItem", "edit"]);
</script>
