<template>
  <div style="height: 100%" v-smart-focus>
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="close"
      :hide-submit="true"
      :loading="loading"
    >
      <template #custom>
        <span v-if="transferNumber">#{{ transferNumber }}</span>
      </template>

      <template #actions>
        <v-btn
          text="Save PR Draft"
          variant="outlined"
          color="primary"
          class="ml-2"
          @click="handleSubmit('PR')"
          :loading="loading"
          :disabled="loading"
        />

        <v-btn
          text="Save PO Draft"
          variant="outlined"
          color="primary"
          class="ml-2"
          @click="handleSubmit('PO')"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid class="mt-2">
      <v-form ref="form" v-model="formValid">
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="cart.deliveryDate"
              label="Delivery Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
        </v-row>
      </v-form>

      <convertToPurchaseTable
        v-if="tableItems.length"
        :itemsList="tableItems"
        :headers="convertPurchaseItemHeaders"
        :cart="cart"
        @removeItem="removeItem"
        @edit="modifyItem"
      />
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";

import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import convertToPurchaseTable from "./convertToPurchaseTable.vue";

import { useInventoryItemStore } from "@/stores/inventoryItem";
import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import { convertPurchaseItemHeaders } from "@/helpers/tableHeaders";
import cartHelper from "@/helpers/cartHelper";
import rules from "@/helpers/rules";
import { purchaseStatus } from "@/constants/status";

const router = useRouter();
const route = useRoute();

const inventoryStore = useInventoryItemStore();
const purchaseRequestStore = usePurchaseRequestStore();

const transferId = route.params.id;
const transferNumber = route.params.transferNumber;
const transferWorkAreaId = route.params.transferWA;

const form = ref(null);
const formValid = ref(false);
const loading = ref(false);

const cart = ref(cartHelper.NewCart("pr"));
const tableItems = ref([]);
const todayDate = new Date().toISOString().split("T")[0];

const close = () => router.push({ name: "transfers" });

const formatItems = () => {
  return tableItems.value
    .filter((item) => item.selected)
    .map((item) => ({
      ordinal: item.ordinal,
      itemId: item.itemId,
      itemName: item.itemName,
      itemCode: item.code,
      categoryId: item.categoryId,
      subcategoryId: item.subCategoryId,
      hsnCode: item.hsnCode,
      vendor: {
        name: item.vendor.vendorName,
        id: item.vendor.vendorId,
      },
      pkg: {
        name: item.requestedPkg,
        id: item.requestedPkgId,
        quantity: item.quantity,
      },
      purchaseUOM: item.purchaseUnit,
      remarks: item.remarks ?? null,
      quantity: item.quantity,
      unitCost: item.unitCost,
      inStock: item.inStock,
      grossAmount: item.grossAmount,
      totalDiscount: item.totalDiscount,
      netAmount: item.netAmount,
      charges: item.charges || [],
      totalChargeAmount: item.totalChargeAmount,
      taxes: item.taxes || [],
      totalTaxAmount: item.totalTaxAmount,
      totalAmount: item.totalAmount,
      totalCess: item.totalCess,
      totalFocAmount: item.totalFocAmount,
      taxRate: item.taxRate,
      roundoff: item.roundoff ?? 0,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      contractPrice: item.contractPrice ?? 0,
      contractType: item.contractType ?? null,
      contractId: item.contractId ?? null,
      contractNumber: item.contractNumber ?? null,
      inclTax: item.inclTax ?? false,
      pkgUOM: item.requestedPkg,
    }));
};

const formatPayload = (status) => ({
  location: cart.value.location,
  inventoryLocation: cart.value.inventoryLocation,
  deliveryDate: cart.value.deliveryDate,
  vendorType: 1,
  items: formatItems(),
  status,
  grossAmount: cart.value.grossAmount,
  totalDiscount: cart.value.totalDiscount,
  netAmount: cart.value.netAmount,
  totalChargeAmount: cart.value.totalChargeAmount,
  totalTaxAmount: cart.value.totalTaxAmount,
  totalAmount: cart.value.totalAmount,
  totalCess: cart.value.totalCess,
  totalFocAmount: cart.value.totalFocAmount,
  taxes: cart.value.taxes,
  charges: cart.value.charges,
  transferNumber: transferNumber,
  transferWorkAreaId: transferWorkAreaId,
});

const handleSubmit = async (type) => {
  const status = purchaseStatus.DRAFT;
  try {
    if (loading.value) return;
    loading.value = true;

    const { valid } = await form.value.validate();
    if (!valid) return;

    const payload = formatPayload(status);

    const response = await purchaseRequestStore.createPurchaseRequest(payload);

    const id = response.id;

    if (type === "PO") {
      if (!id) return;
      await purchaseRequestStore.approvePurchaseRequest(id);
      await purchaseRequestStore.convertToPurchaseOrder(id);
      router.push({ name: "purchase-orders" });
    } else {
      router.push({ name: "purchase-requests" });
    }
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = [...cart.value.items];
};

const removeItem = (index) => {
  cart.value = cartHelper.RemoveItem(cart.value, index);
  tableItems.value = [...cart.value.items];
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    const result = await inventoryStore.getTransferDetails(transferId);

    cart.value.deliveryDate = result.deliveryDate
      ? result.deliveryDate
      : todayDate;

    const items = Array.isArray(result) ? result : result.items;

    cart.value.location = items[0]?.location;
    cart.value.inventoryLocation = items[0]?.inventoryLocation;

    cart.value.items = [];

    items.forEach((item) => {
      item.totalDiscount = item.totalDiscount ?? 0;
      item.totalCess = item.totalCess ?? 0;
      item.taxes = item.taxes ?? [];

      cart.value = cartHelper.AddItem(cart.value, item);
    });

    tableItems.value = cart.value.items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>
