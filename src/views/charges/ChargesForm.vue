<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card class="custom-card">
      <v-card-title
        class="d-flex justify-space-between align-center dialog-title-background"
      >
        <p>{{ isEdit ? "Edit Charges" : "Create Charges" }}</p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4 pb-0" style="padding: 15px">
        <v-form ref="form">
          <v-text-field
            v-model.trim="charge.name"
            class="mb-4"
            label="Name*"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"
          />

          <v-autocomplete
            class="mb-4"
            v-model="charge.valueType"
            :items="[
              { name: 'Percentage (%)', value: 'percentage' },
              { name: 'Amount', value: 'amount' },
            ]"
            label="Value Type"
            item-title="name"
            item-value="value"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            :rules="[rules.require]"
          />

          <v-text-field
            v-if="charge.valueType == 'amount'"
            class="required-fld mb-4"
            v-model.number="charge.valueAmt"
            label="Charge in Amount"
            variant="outlined"
            density="compact"
            hide-details="auto"
            color="primary"
            :rules="[rules.require]"
          />

          <v-text-field
            v-if="charge.valueType == 'percentage'"
            class="required-fld mb-4"
            v-model.number="charge.valuePercentage"
            label="Charge in Percentage (%)"
            variant="outlined"
            density="compact"
            hide-details="auto"
            suffix="%"
            color="primary"
            :rules="[rules.require]"
          />
        </v-form>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex flex-column mx-2 mb-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { useChargeStore } from "@/stores/Charge";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";
import { chargeRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

const props = defineProps({
  isEdit: Boolean,
  chargesData: Object,
});

const chargeStore = useChargeStore();
const model = defineModel();

const form = ref(null);
const loadBtn = ref(null);

const charge = ref({ ...DEFAULT_RECORD });
const emit = defineEmits(["saved"]);

const cleanName = () => {
  charge.value.name = formatName(charge.value.name);
};

const resetForm = () => {
  charge.value = {
    name: "",
    valueType: "percentage",
    valueAmt: 0,
    valuePercentage: 0,
  };
};

watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.chargesData) {
    charge.value = {
      name: props.chargesData.name,
      valueType: props.chargesData.valueType,
      valueAmt: props.chargesData.valueAmt,
      valuePercentage: props.chargesData.valuePercentage,
    };
  } else {
    resetForm();
  }
});

const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = {
    name: charge.value.name,
    valueType: charge.value.valueType,
  };

  if (charge.value.valueType === "amount") {
    payload.valueAmt = charge.value.valueAmt;
  }

  if (charge.value.valueType === "percentage") {
    payload.valuePercentage = charge.value.valuePercentage;
  }

  try {
    if (props.isEdit && props.chargesData?.id) {
      await chargeStore.updateCharge(props.chargesData.id, payload);
    } else {
      await chargeStore.createCharge(payload);
    }

    emit("saved");
    loadBtn.value = null;
    if (closeAfter) model.value = false;
    else resetForm();
  } catch (error) {
    console.error("Error saving charge:", error);
    loadBtn.value = null;
  }
};

const closeDialog = () => {
  model.value = false;
};
</script>
