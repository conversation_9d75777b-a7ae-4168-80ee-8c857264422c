<template>
  <div class="taxes-list">
    <list-actions-bar
      v-model="filterState"
      search-label="Search Charges"
      add-label="Charges"
      @refresh="refresh"
      @add="add"
      :filters="filters"
      @apply-filters="applyFilters"
      :hideImportExport="true"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No charges found"
          >
            <template #loading>
              <v-skeleton-loader type="table-row@10" />
            </template>

            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary" />
            </template>

            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >
                {{ item.name }}
              </span>
            </template>

            <template #item.value="{ item }">
              <span v-if="item.valueType == 'percentage'">
                {{ item.valuePercentage }}%</span
              ><span v-else>
                {{ item.valueAmt }}
              </span>
            </template>

            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Charge"
                :id="item.id"
                :name="item.name"
                activate-url="/charges/:id/activate"
                deactivate-url="/charges/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <charges-form
      v-model="dialog"
      :isEdit="isEdit"
      :chargesData="chargesData"
      @saved="refresh"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";
import { useChargeStore } from "@/stores/Charge";
import { tableHeaders } from "@/helpers/tableHeaders";
import { vendorFilters } from "@/helpers/filterConfig";
import { filterData } from "@/helpers/searchFilter";
import chargesForm from "./ChargesForm.vue";

const chargeStore = useChargeStore();

const headers = tableHeaders.charges;
const sortBy = ref([{ key: "name", order: "asc" }]);

const filters = vendorFilters;
const filterState = ref({
  search: "",
  filters: { status: null },
});

const loading = ref(false);
const dialog = ref(false);
const isEdit = ref(false);
const chargesData = ref(null);

const charges = computed(() => chargeStore.getCharges || []);

const filteredItems = computed(() => {
  const query = filterState.value?.search?.toLowerCase() || "";
  let result = query ? filterData(charges.value, query) : charges.value;
  return result;
});

const filteredHeaders = computed(() => headers.value.filter((h) => h.enable));

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  chargesData.value = null;
};

const edit = async (id) => {
  try {
    const data = await chargeStore.fetchChargeById(id);
    chargesData.value = data;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error("Error fetching charge:", error);
  }
};

const resetFilter = () => {
  filterState.value = {
    search: null,
    filters: { status: null },
    columns: headers,
    options: {},
  };
};

const fetch = async () => {
  await chargeStore.fetchCharges();
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const applyFilters = ({ columns }) => {
  if (columns) tableHeaders.setCharges(columns);
};

onBeforeMount(() => {
  refresh();
});
</script>
