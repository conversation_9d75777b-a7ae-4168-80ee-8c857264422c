<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    autofocus
                  />
                </v-col>

                <v-col cols="12" v-if="packageList.length">
                  <v-autocomplete
                    class="required-fld"
                    v-model="formData.pkg"
                    :items="filteredPackages"
                    item-title="name"
                    item-value="id"
                    label="Package"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="handleInventoryPkg"
                    :disabled="packageList.length === 1"
                  />
                </v-col>

                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.adjustmentType"
                    :items="types"
                    item-title="name"
                    item-value="id"
                    label="Adjustment Type"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="onAdjustmentTypeChanged"
                  />
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.adjustmentQuantity"
                    label="Adjustment Qty"
                    type="number"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${formData.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'adjustmentQuantity')"
                    @update:model-value="
                      (val) => {
                        const inStock = formData.inStock || 0;
                        if (val > inStock && formData.adjustmentType?.id === 'reduction') {
                          formData.adjustmentQuantity = inStock;
                        }                        
                      }
                    "
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="formData.reason"
                    label="Reason"
                    :rules="[rules.requireTrimmed]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details
                    rows="2"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, computed, reactive, watch } from "vue";
import rules from "@/helpers/rules";
import { transferRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  inventoryLocationId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
});

const inventoryStore = useInventoryItemStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packageList = ref([]);
const selectedItem = ref([]);
const itemField = ref(null);

const types = ref([
  { id: "addition", name: "Stock Addition" },
  { id: "reduction", name: "Stock Reduction" },
]);

const onAdjustmentTypeChanged = (v) => {  
  formData.value.adjustmentType = v;
  reset();
};

const reset = () => {  
  formData.value.adjustmentQuantity = null;
  formData.value.reason = "";
};

const filteredPackages = computed(() => {
  if (!selectedItem.value || !selectedItem.value.id) {
    return [];
  }
  const selectedPkgs = props.existingItems
    .filter((i) => i.itemId === selectedItem.value.id)
    .map((i) => i.pkg?.id);
  return packageList.value.filter((pkg) => !selectedPkgs.includes(pkg.id));
});

watch(filteredPackages, (list) => {
  if (list.length > 0) {
    formData.value.pkg = list[0];
    handleInventoryPkg(formData.value.pkg);
  } else {
    formData.value.pkg = null;
  }
});

const onSelectInventoryItem = async (selected) => {  
  selectedItem.value = selected;

  packageList.value = [...selected.packages];

  if (selected.defaultPackage) {
    packageList.value.unshift({
      name: `${selected.purchaseUnit}`,
      id: "default",
    });
  }
  reset();
};

const handleInventoryPkg = async (v) => {
  if (!v) return;
  reset();

  const item = await inventoryStore.fetchItemDetails(
    {
      id: selectedItem.value.id,
      locationId: props.locationId,
      inventoryLocationId: props.inventoryLocationId,
      pkgId: v.id,
    },
    false
  );

  formData.value.itemName = item.itemName;
  formData.value.itemCode = item.itemCode;
  formData.value.itemId = item.id;
  formData.value.categoryId = item.category?.id;
  formData.value.categoryName = item.category?.name;
  formData.value.subCategoryId = item.subCategory?.id;
  formData.value.subCategoryName = item.subCategory?.name;
  formData.value.countingUOM = item.countingUnit.symbol;
  formData.value.adjustmentQuantity = item.adjustmentQuantity;
  formData.value.purchaseUOM =
    v.id === "default" ? item.purchaseUnit.symbol : v.packageCode;
  formData.value.inStock = item.inStock;
  formData.value.inclTax = item.inclTax || false;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {  
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();
  },
  {
    immediate: true,
  }
);
</script>
