<template>
  <v-dialog v-model="model" width="850" persistent>
    <v-card>
      <v-card-title
        class="d-flex justify-space-between align-center"
      >
        <p>
          {{ isEdit ? "Edit Location" : "Create Location" }}
        </p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider class="mb-4"/>

      <v-card-text class="py-4">
        <v-form ref="form">
          <v-row class="gap-4" >
            <v-col cols="12" md="6">
              <v-text-field
                class="required-fld"
                v-model.trim="store.name"
                label="Location Name"
                color="primary"
                variant="outlined"
                density="compact"
                hide-details="auto"
                @blur="cleanName"
                :rules="[rules.require, rules.maxLength(100)]"
                :readonly="store.locationType?.toUpperCase() === 'OUTLET'"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-autocomplete
                class="required-fld"
                v-model="store.locationType"
                :items="['CENTRAL KITCHEN','CENTRAL WAREHOUSE']"
                label="Location Type"
                hide-details
                variant="outlined"
                density="compact"
                color="primary"
                :clearable="!(isEdit || store.locationType?.toUpperCase() === 'OUTLET')"
                :rules="[rules.require]"
                :readonly="isEdit || store.locationType?.toUpperCase() === 'OUTLET'"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model.trim="store.panNo"
                label="PAN No."
                color="primary"
                variant="outlined"
                density="compact"
                hide-details="auto"
                :rules="[rules.panNumber]"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model.trim="store.gstNo"
                label="GST No."
                color="primary"
                variant="outlined"
                density="compact"
                hide-details="auto"
                :rules="[rules.gstNumber]"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-textarea
                v-model.trim="store.billTo"
                label="Bill To"
                color="primary"
                variant="outlined"
                density="compact"
                :rows="2"
                :max-rows="6"
                hide-details="auto"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-textarea
                v-model.trim="store.shipTo"
                label="Ship To"
                color="primary"
                variant="outlined"
                density="compact"
                :rows="2"
                :max-rows="6"
                hide-details="auto"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider class="mt-3"/>

      <v-card-actions class="d-flex justify-space-between ma-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex align-center">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
            class="mx-3"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";
import { storeRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

const props = defineProps({
  isEdit: Boolean,
  storeData: Object,
});

const model = defineModel();
const emit = defineEmits(["createStore", "updateStore"]);

const form = ref(null);

const store = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const resetForm = () => {
  store.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
};

const cleanName = () => {
  store.value.name = formatName(store.value.name);
};

watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.storeData) {
    store.value = { ...props.storeData };
  } else {
    resetForm();
  }
});

const loadBtn = ref(null);

const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = { ...store.value };

  const done = (success) => {
    loadBtn.value = null;
    if (!success) return;
    if (closeAfter) {
      model.value = false;
    } else {
      resetForm();
    }
  };

  if (props.isEdit) {
    emit("updateStore", payload, done);
  } else {
    emit("createStore", payload, done);
  }
};

const closeDialog = () => {
  model.value = false;
};
</script>
