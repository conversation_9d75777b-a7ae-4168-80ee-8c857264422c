<template>
  <div class="inventory-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      v-model="filterState"
      search-label="Search Inventory Items"
      add-label="Inventory Item"
      sheets="Inventory Items"
      @refresh="refresh"
      @add="add"
      :filters="filters"
      @apply-filters="applyFilters"
    />

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No inventory items found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.itemName="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.itemName }}</span>
              <v-icon v-if="!item.stockable" icon="mdi-fruit-cherries" color="error"/>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Inventory Item"
                :id="item.id"
                :name="item.itemName"
                activate-url="/inventory-items/:id/activate"
                deactivate-url="/inventory-items/:id/deactivate"
                @update="fetch"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
            <template #item.vendors="{ item }">
              {{
              item.vendors.map((vendor) => vendor.name).join(", ")
              }}
            </template>
            <template #item.purchaseUnit="{ item }">{{ item.purchaseUnit.name }}</template>
            <template #item.countingUnit="{ item }">{{ item.countingUnit.name }}</template>
            <template #item.recipeUnit="{ item }">{{ item.recipeUnit.name }}</template>
            <!-- <template #item.actions="{ item }">
              <v-btn
                        variant="text"
                        density="compact"
                        size="medium"
                        @click="viewUsageDetails(item)"
                      >
                        <v-icon size="20">mdi-dots-vertical</v-icon>
                      </v-btn>
            </template>-->
            <template #item.category="{ item }">{{ item.category.name }}</template>
            <template #item.subCategory="{ item }">{{ item.subCategory.name }}</template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <!-- <usage-details-dialog
      v-if="viewDetailsDialog"
      v-model="viewDetailsDialog"
      :selected-item="selectedDetailsItem"
    ></usage-details-dialog>-->
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useInventoryItemStore } from "@/stores/inventoryItem";
import { tableHeaders } from "@/helpers/tableHeaders";
import { inventoryItemsFilters } from "@/helpers/filterConfig";
import { filterData } from "@/helpers/searchFilter";
// import UsageDetailsDialog from "@/components/inventoryItem/UsageDetailsDialog.vue";

const inventoryItemStore = useInventoryItemStore();
const router = useRouter();

const loading = ref(false);
const headers = ref(tableHeaders["inventoryItems"]);
const sortBy = ref([{ key: "itemName", order: "asc" }]);
const filters = inventoryItemsFilters;
const filterState = ref(null);

const items = computed(() => inventoryItemStore.getInventoryItems || []);

const filteredItems = computed(() => {
  const { search, filters } = filterState.value || {};
  const query = search?.toLowerCase();
  let result = query ? filterData(items.value, query) : [...items.value];
  const { status, itemType } = filters;
  return result.filter(
    item =>
      (status === null || item.activeStatus === status) &&
      (itemType === null || item.itemType === itemType)
  );
});

const applyFilters = ({ columns }) => {
  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setInventoryItems"](columns);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.enable));

const add = () => {
  router.push({ name: "create-inventory-item" });
};

const edit = id => {
  router.push({ name: "edit-inventory-item", params: { id } });
};

const toggleActivate = async ({ id }) => {
  try {
    await inventoryItemStore.updateInventoryItemActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const resetFilter = () => {
  filterState.value = {
    search: null,
    filters: {
      status: null,
      itemType: null
    },
    columns: headers,
    options: {}
  };
};

const fetch = async () => {
  await inventoryItemStore.fetchInventoryItems();
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

/*
const viewDetailsDialog = ref(false);
const selectedDetailsItem = ref(null);

const viewUsageDetails = (item) => {
  viewDetailsDialog.value = true;
  selectedDetailsItem.value = item;
};
*/

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped>
</style>
