<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :madeItemOnly="true"
                    autofocus
                  />
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.quantity"
                    label="Recipe Qty"
                    type="number"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${formData.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'quantity')"  
                  >
                  </v-text-field>
                </v-col>  
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, watch } from "vue";
import rules from "@/helpers/rules";
import { transferRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  inventoryLocationId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
});

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const selectedItem = ref([]);
const itemField = ref(null);

const onSelectInventoryItem = async (selected) => {  
  selectedItem.value = selected;
  formData.value.itemName = selected.name;
  formData.value.itemCode = selected.code;
  formData.value.itemId = selected.id;
  formData.value.countingUOM = selected.countingUnit.symbol;
  formData.value.purchaseUOM = selected.purchaseUnit.symbol;
  formData.value.recipeUOM = selected.recipeUnit.symbol;
  formData.value.cost = selected.unitCostIncludingTax;
  formData.value.recipeId = selected.recipeId;
  formData.value.categoryId = selected.categoryId;
  formData.value.categoryName = selected.categoryName;
  formData.value.subCategoryId = selected.subCategoryId;
  formData.value.subCategoryName = selected.subCategoryName;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };  
  emit("add", payload);

  // Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();
  },
  {
    immediate: true,
  }
);
</script>
