<template>
  <div>
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      :back-to="{ name: 'menu-recipes' }"
      hide-submit
      :loading="loading"
    >
      <template #custom>#{{ record.menuRecipeNumber }}</template>
    </form-actions-bar>

    <!-- loader -->
    <page-loader v-if="loading" />

    <!-- Overview/Content -->
    <v-container v-else fluid>
      <!-- Overview -->
      <MenuRecipeOverview :data="record" />

      <!-- Item Table -->
      <MenuRecipeTableItems :items="tableItems" :record="record" />
    </v-container>
  </div>
</template>
<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute } from "vue-router";
import { useMenuRecipeStore } from "@/stores/menuRecipe";

// Components
import PageLoader from "@/components/utils/PageLoader.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import MenuRecipeTableItems from "./MenuRecipeTableItems.vue";
import MenuRecipeOverview from "./MenuRecipeOverview.vue";

const menuRecipeStore = useMenuRecipeStore();
const route = useRoute();

const id = route.params.id;
const loading = ref(false);
const record = ref({});
const tableItems = ref([]);

const get = async () => {
  loading.value = true;
  try {
    const { items, ...result } = await menuRecipeStore.fetchMenuRecipeById(id);
    record.value = result;
    tableItems.value = items;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await get();
});
</script>
