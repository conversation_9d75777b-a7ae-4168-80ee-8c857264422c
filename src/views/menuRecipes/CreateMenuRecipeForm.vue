<template>
    <div style="height: 100%">
      <page-loader v-if="loading" />
  
      <!-- Form Actions Bar -->
      <form-actions-bar
        v-if="!loading"
        @close="navigatePrevious"
        :hide-submit="true"
        :loading="loading"
      >
        <template #actions>
          <v-btn
            text="Submit"
            @click="create"
            variant="flat"
            color="primary"
            class="ml-2"
            :loading="loading"
            :disabled="loading"
          />

        </template>
      </form-actions-bar>
  
      <v-container v-if="!loading" fluid>
        <v-form ref="form" v-model="formValid">
          <v-row>
            <!-- From Location -->
            <v-col cols="12" sm="6" md="3">
              <location-field
                v-model="location"
                label="Location"
                hint="Location"
                persistent-hint
                mandatory
                return-object
                @update:model-value="locationChange"
              />
            </v-col>

            <!-- From WorkArea -->
            <v-col cols="12" sm="6" md="3">
              <work-area-field
                ref="fromRef"
                v-model="workArea"
                label="WorkArea"
                hint="WorkArea"
                persistent-hint
                mandatory
                return-object
                :locations="location ? [location.id] : null"
                :disabled="!location"
                @update:model-value="workAreaChange"
              />
            </v-col>
          </v-row>
        </v-form>
  
        <!-- Table -->
        <menu-recipe-table
          :recipeList="tableItems"
          :formValid="formValid"
          @addItem="openForm = true"
          @removeItem="remove"
        />
  
        <menu-recipe-item-form
          v-if="location && workArea"
          v-model="openForm"
          :formValid="formValid"
          :locationId="location ? location.id : null"
          :inventoryLocationId="workArea ? workArea.id : null"
          @add="add"
          :existingItems="tableItems"
        />
      </v-container>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, nextTick, inject } from "vue";
  import { useRouter } from "vue-router";
  import { useMenuRecipeStore } from "@/stores/menuRecipe";
  import { useSnackbarStore } from "@/stores/snackBar";
  import FormActionsBar from "@/components/utils/FormActionsBar.vue";
  import PageLoader from "@/components/utils/PageLoader.vue";
  import WorkAreaField from "@/components/fields/WorkAreaField.vue";
  import LocationField from "@/components/fields/LocationField.vue";
  import MenuRecipeItemForm from "./MenuRecipeItemForm.vue";  
  import MenuRecipeTable from "./MenuRecipeTable.vue";

  const router = useRouter();
  const { showSnackbar } = useSnackbarStore();
  const menuRecipeStore = useMenuRecipeStore();
  const $confirm = inject("confirm");

  
  const openForm = ref(false);
  const form = ref(null);
  const formValid = ref(false);
  const loading = ref(false);

  const location = ref(null);
  const workArea = ref(null);

  const fromRef = ref(null);

  const tableItems = ref([]);

  const add = (data) => {
    tableItems.value.unshift(data);
  };
  
  const remove = (index) => {
    tableItems.value.splice(index, 1);
  };

  const create = async () => {
    const { valid } = await form.value.validate();
    if (!valid) return;

    if (!tableItems.value.length) {
      showSnackbar("primary", "At least one item is required");
      return;
    }
    loading.value = true;
    try {
      const payload = {
        locationId: location.value?.id,
        locationName: location.value?.name,
        workAreaId: workArea.value?.id,
        workAreaName: workArea.value?.name,
        items: tableItems.value.map((i) => ({
          itemId: i.itemId,
          itemName: i.itemName,
          itemCode: i.itemCode,
          categoryId: i.categoryId,
          subcategoryId: i.subCategoryId,
          categoryName: i.categoryName,
          subcategoryName: i.subCategoryName,
          quantity: i.quantity,
          countingUOM: i.countingUOM,
          purchaseUOM: i.purchaseUOM,
          recipeUOM: i.recipeUOM,
          recipeId: i.recipeId,
          // cost: i.cost,
          // pkg: {
          //   ...i.pkg,
          //   id: i.pkg?.id,
          //   name: i.pkg?.name,
          // },
        })),
      };      

      await menuRecipeStore.createMenuRecipe(payload);

      // Clear form data after successful creation
      tableItems.value = [];
      location.value = null;
      workArea.value = null;

      navigatePrevious();
    } catch (err) {
      console.error(err);
    } finally {
      loading.value = false;
    }
  };
  
  const navigatePrevious = () => router.push({ name: "menu-recipes" });

  const locationChange = async (loc) => {
    // Check if table has items and show confirmation dialog
    if (tableItems.value.length > 0) {
      const confirmed = await $confirm(
        "Changing the location will reset the table data. Do you want to proceed?",
        { title: "Reset Table Data", confirmText: "Yes" }
      );
      if (!confirmed) {
        return; // Do not proceed if user cancels
      }
      // Clear table data if user confirms
      tableItems.value = [];
    }

    location.value = loc;
    workArea.value = null;
    if (loc && fromRef.value) {
      fromRef.value.setDefault(loc.inventoryLocationId);
    }
  };

  const workAreaChange = async (workArea) => {
    // Check if table has items and show confirmation dialog
    if (tableItems.value.length > 0) {
      const confirmed = await $confirm(
        "Changing the location will reset the table data. Do you want to proceed?",
        { title: "Reset Table Data", confirmText: "Yes" }
      );
      if (!confirmed) {
        return; // Do not proceed if user cancels
      }
      // Clear table data if user confirms
      tableItems.value = [];
    }

    workArea.value = workArea;
  }
  
  const focusFirstField = () => {
    // Focus on the first available field (From Location)
    const firstInput = document.querySelector('.v-input input');
    if (firstInput) firstInput.focus();
  };

  onMounted(async () => {
    await nextTick();
    focusFirstField();

    window.addEventListener('keydown', (e) => {
      if (e.key === 'Tab' && document.activeElement === document.body) {
        e.preventDefault();
        focusFirstField();
      }
    });
  });
  
  </script>
  