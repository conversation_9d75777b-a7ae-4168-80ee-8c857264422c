<template>
  <form-actions-bar
    @close="navigatePrevious"
    @submit="save"
    :loading="isLoading"
  />
  <v-container v-if="!loader" max-width="600">
    <v-card-text class="pt-4">
      <v-form ref="form">
        <v-row>
          <v-col cols="12">
            <v-text-field
              class="required-fld"
              v-model="record.name"
              label="Category Name"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              @blur="cleanName"
              :rules="[rules.require]"
            />
          </v-col>
          <v-col cols="12">
            <v-text-field
              v-model.trim="subCategoryInput"
              label="Sub-Category"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              @keyup.enter="addSubCategory()"
              :rules="[
                () =>
                  record.subCategories.length > 0
                    ? true
                    : 'Sub-Category is required',
                (v) =>
                  v && isDuplicateSubCategory
                    ? 'Duplicates are not allowed'
                    : true
              ]"
            >
              <template #append-inner>
                <v-tooltip text="ADD" location="top">
                  <template #activator="{ props }">
                    <v-icon
                      v-bind="props"
                      color="primary"
                      class="cursor-pointer"
                      :disabled="isDuplicateSubCategory"
                      @click="addSubCategory()"
                    >
                      mdi-plus
                    </v-icon>
                  </template>
                </v-tooltip>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12" class="d-flex flex-wrap ga-2">
            <v-chip
              v-for="sub in record.subCategories"
              :key="sub.name"
              closable
              color="primary"
              variant="elevated"
              @click:close="removeSubCategory(sub.name)"
            >
              {{ sub.name }}
            </v-chip>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { categoryRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useCategoryStore } from "@/stores/category";
import { formatName } from "@/helpers/formatter";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";

const route = useRoute();
const router = useRouter();
const categoryStore = useCategoryStore();
const categoryId = route.params.id;
const isEdit = categoryId !== undefined;

const isLoading = ref(false);
const form = ref(null);
const subCategoryInput = ref("");
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const loader = ref(false);

const isDuplicateSubCategory = computed(() => {
  const input = formatName(subCategoryInput.value).toLowerCase();
  return record.value.subCategories.some(
    (s) => formatName(s.name).toLowerCase() === input
  );
});

onBeforeMount(async () => {
  if (isEdit) {
    loader.value = true;

    try {
      const result = await categoryStore.fetchCategoryById(categoryId);
      record.value = result;
      loader.value = false;
    } catch (error) {
      console.log(error);
    }
  }
});

const save = async () => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      isLoading.value = false;
      return;
    }

    if (isEdit) await categoryStore.updateCategory(record.value);
    else await categoryStore.createCategory(record.value);
    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const navigatePrevious = () => {
  router.push({ name: "categories" });
};

const addSubCategory = () => {
  const name = formatName(subCategoryInput.value);
  if (!name || isDuplicateSubCategory.value) return;

  record.value.subCategories.push({ name });
  subCategoryInput.value = "";
};

const removeSubCategory = (name) => {
  const index = record.value.subCategories.findIndex((s) => s.name === name);
  if (index !== -1) {
    record.value.subCategories.splice(index, 1);
  }
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
};
</script>
