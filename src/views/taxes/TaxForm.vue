<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card class="custom-card">
      <v-card-title
        class="d-flex justify-space-between align-center dialog-title-background"
      >
        <p>{{ isEdit ? "Edit Tax" : "Create Tax" }}</p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4" style="padding: 15px">
        <v-form ref="form">
          <!-- Tax Name -->
          <v-text-field
            class="required-fld mb-4"
            v-model.trim="tax.name"
            label="Name"
            color="primary"
            variant="outlined"
            density="compact"
            hide-details="auto"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"
          />

          <v-autocomplete
            class="mb-4"
            v-model="tax.taxLevel"
            :items="[
              { name: 'Item', value: 'item' },
              { name: 'GRN', value: 'grn' },
            ]"
            label="Tax Level"
            item-title="name"
            item-value="value"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            :rules="[rules.require]"
            @update:model-value="handleTaxLevel"
          />

          <v-autocomplete
            class="mb-4"
            v-model="tax.valueType"
            :items="filteredTaxTypes"
            label="Value Type"
            item-title="name"
            item-value="value"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            :rules="[rules.require]"
          />

          <!-- Tax value -->
          <v-text-field
            v-if="tax.valueType == 'amount'"
            class="required-fld mb-4"
            v-model.number="tax.valueAmt"
            label="Tax in Amount"
            variant="outlined"
            density="compact"
            hide-details="auto"
            color="primary"
            :rules="[rules.require]"
          />

          <v-text-field
            v-if="tax.valueType == 'percentage'"
            class="required-fld mb-4"
            v-model.number="tax.valuePercentage"
            label="Tax in Percentage (%)"
            variant="outlined"
            density="compact"
            hide-details="auto"
            suffix="%"
            color="primary"
            :rules="[rules.require]"
          />

          <!-- Sub Taxes -->
          <v-row>
            <v-col cols="6">
              <v-text-field
                v-model.trim="subTaxName"
                label="Sub Tax name"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              />
            </v-col>

            <v-col cols="6" v-if="tax.valueType == 'amount'">
              <v-text-field
                v-model.number="subTaxValueAmt"
                label="Value"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @keyup.enter="addSubTax"
              />
            </v-col>

            <v-col cols="6" v-if="tax.valueType == 'percentage'">
              <v-text-field
                v-model.number="subTaxValuePercentage"
                label="Value (%)"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                suffix="%"
                @keyup.enter="addSubTax"
              />
            </v-col>
          </v-row>

          <div class="d-flex justify-end mb-2">
            <v-tooltip text="ADD" location="top">
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  color="primary"
                  variant="tonal"
                  size="small"
                  class="mt-3"
                  :disabled="isSubTaxInvalid"
                  @click="addSubTax"
                >
                  <v-icon start>mdi-plus</v-icon>
                  Add Sub-Tax
                </v-btn>
              </template>
            </v-tooltip>
          </div>

          <p v-if="subTaxError" class="text-error text-caption mt-n2 mb-3">
            {{ subTaxError }}
          </p>

          <v-row>
            <v-col cols="12" class="d-flex flex-wrap ga-2">
              <v-chip
                v-for="sub in tax.components"
                :key="sub.name"
                closable
                color="primary"
                variant="elevated"
                @click:close="removeSubTax(sub.name)"
              >
                {{ sub.name }} -
                {{
                  tax.valueType == "amount"
                    ? sub.valueAmt
                    : sub.valuePercentage + "%"
                }}
              </v-chip>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex flex-column mx-2 mb-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            @click="submit(true)"
            :loading="loadBtn === 'save'"
            :disabled="loadBtn !== null"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="loadBtn === 'continue'"
            :disabled="loadBtn !== null"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { useTaxStore } from "@/stores/taxes";
import { formatName } from "@/helpers/formatter";
import { taxRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import rules from "@/helpers/rules";

const props = defineProps({
  isEdit: Boolean,
  taxData: Object,
  refresh: Function,
});

const model = defineModel();
const form = ref(null);
const loadBtn = ref(null);

const tax = ref({ ...DEFAULT_RECORD });
const subTaxName = ref("");
const subTaxValueAmt = ref(null);
const subTaxValuePercentage = ref(null);
const subTaxError = ref("");

const taxStore = useTaxStore();
const emit = defineEmits(["saved"]);

const taxTypes = ref([
  { name: "Percentage (%)", value: "percentage" },
  { name: "Amount", value: "amount" },
]);

const filteredTaxTypes = computed(() => {
  if (tax.value.taxLevel == "item")
    return taxTypes.value.filter((t) => t.value == "percentage");

  return taxTypes.value;
});

// Format name
const cleanName = () => {
  tax.value.name = formatName(tax.value.name);
};

// Total of sub taxes
const totalSubTaxValue = computed(() => {
  return tax.value.components.reduce((sum, s) => {
    if (tax.value.valueType === "amount") return sum + Number(s.valueAmt || 0);
    return sum + Number(s.valuePercentage || 0);
  }, 0);
});

// Reset form
const resetForm = () => {
  tax.value = {
    name: "",
    valueType: "percentage",
    valueAmt: 0,
    valuePercentage: 0,
    taxLevel: "item",
    components: [],
  };

  subTaxName.value = "";
  subTaxValueAmt.value = 0;
  subTaxValuePercentage.value = 0;
  subTaxError.value = "";
};

// Watch dialog open
watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.taxData) {
    tax.value = {
      name: props.taxData.name,
      valueType: props.taxData.valueType,
      valueAmt: props.taxData.valueAmt,
      valuePercentage: props.taxData.valuePercentage,
      taxLevel: props.taxData.taxLevel,
      components: props.taxData.components || [],
    };
  } else {
    resetForm();
  }
});

// Validity of sub-tax input
const isSubTaxInvalid = computed(() => {
  const name = formatName(subTaxName.value);
  const vAmt = Number(subTaxValueAmt.value);
  const vPct = Number(subTaxValuePercentage.value);

  const mainValue =
    tax.value.valueType === "amount"
      ? tax.value.valueAmt
      : tax.value.valuePercentage;

  const subValue = tax.value.valueType === "amount" ? vAmt : vPct;

  if (!name || !subValue) return true;

  if (mainValue && totalSubTaxValue.value + subValue > mainValue) {
    subTaxError.value = "Total sub-tax value cannot exceed the main tax value.";
    return true;
  }

  subTaxError.value = "";
  return false;
});

// Add sub-tax
const addSubTax = () => {
  if (isSubTaxInvalid.value) return;

  const name = formatName(subTaxName.value);

  if (
    tax.value.components.some(
      (s) => s.name.toLowerCase() === name.toLowerCase()
    )
  )
    return;

  const entry =
    tax.value.valueType === "amount"
      ? { name, valueAmt: Number(subTaxValueAmt.value) }
      : { name, valuePercentage: Number(subTaxValuePercentage.value) };

  tax.value.components.push(entry);

  subTaxName.value = "";
  subTaxValueAmt.value = null;
  subTaxValuePercentage.value = null;
};

// Remove sub-tax
const removeSubTax = (name) => {
  tax.value.components = tax.value.components.filter((s) => s.name !== name);
};

// Submit
const submit = async (closeAfter = true) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  loadBtn.value = closeAfter ? "save" : "continue";

  const payload = {
    name: tax.value.name,
    valueType: tax.value.valueType,
    taxLevel: tax.value.taxLevel,
    components: tax.value.components,
  };

  if (tax.value.valueType === "amount") {
    payload.valueAmt = tax.value.valueAmt;
  }

  if (tax.value.valueType === "percentage") {
    payload.valuePercentage = tax.value.valuePercentage;
  }

  try {
    if (props.isEdit && props.taxData?.id) {
      await taxStore.updateTax(props.taxData.id, payload);
    } else {
      await taxStore.createTax(payload);
    }

    emit("saved");
    loadBtn.value = null;

    if (closeAfter) model.value = false;
    else resetForm();
  } catch (error) {
    console.error("Error saving tax:", error);
    loadBtn.value = null;
  }
};

// Close dialog
const closeDialog = () => {
  model.value = false;
};

const handleTaxLevel = (val) => {
  if (val == "grn") {
    tax.value.valueType = "amount";
  } else {
    tax.value.valueType = "percentage";
  }
};
</script>
