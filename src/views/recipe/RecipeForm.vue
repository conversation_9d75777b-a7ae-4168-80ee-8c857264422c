<template>
  <div style="height: 100%">

    <!-- Form Actions Bar -->
    <form-actions-bar
      @close="navigatePrevious"
      @submit="save"
      :loading="loading"
    />

    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-model="formValid">
        <v-row>
          <v-col cols="4">
            <v-text-field
              class="required-fld"
              v-model.trim="record.name"
              label="Recipe Name"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              @blur="cleanName"
              :rules="[rules.require, rules.maxLength(100)]"
            />
          </v-col>
          <v-col cols="4">
            <v-text-field
              v-model.trim="record.recipeCode"
              label="Recipe Code"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              :rules="[rules.minLength(4), rules.maxLength(20)]"
            />
          </v-col>
          <v-col cols="4">
            <v-text-field
              class="required-fld"
              v-model.number="record.quantity"
              label="Quantity"
              type="number"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              @keydown.up.prevent
              @keydown.down.prevent
              :rules="[rules.require, rules.quantity, rules.positive]"
            />
          </v-col>
          <v-col cols="4">
            <HouseUnitField
              v-model="record.recipeUnit"
              label="Recipe Unit"
              mandatory
              return-object
              :show-conversion="true"
            ></HouseUnitField>
          </v-col>
          <v-col cols="4">
            <v-autocomplete
              class="required-fld"
              v-model="record.recipeType"
              :items="recipeTypes"
              label="Recipe Type"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              item-title="name"
              item-value="id"
              :rules="[rules.require]"
              clearable
            />
          </v-col>
          <v-col cols="4">
            <TagField v-model="record.tags" label="Tags" return-object :multiple="true" />
          </v-col>
        </v-row>
      </v-form>

      <v-row class="my-1">
        <v-col
          v-for="(card, index) in serviceTypeCards"
          :key="card.name"
          cols="2"
        >
          <v-chip
            :color="chipColors[index % chipColors.length]"
            class="pa-4 w-100 d-flex justify-center"
            rounded="lg"
            variant="elevated"
            style="height: 60px;"
          >
            <div class="d-flex flex-column text-center w-100">
              <span class="text-subtitle-2" style="opacity: 0.7;">
                {{ card.name === "Total" ? "Total Cost" : card.name + " Total Cost" }}
              </span>
              <span class="text-subtitle-2" style="opacity: 0.7;">
                {{ card.total }}
              </span>
            </div>
          </v-chip>
        </v-col>
      </v-row>

      <!-- Table -->
      <recipe-table
        :recipeList="ingredients"
        @addItem="openForm = true"
        @removeItem="remove"
      />

      <recipe-item-form
        v-model="openForm"
        @add="add"
        :existingItems="ingredients"
      />
    </v-container>

    <v-container
      v-else
      class="d-flex justify-center align-center"
      style="height: 100%"
    >
      <div class="text-center">
        <v-progress-circular color="primary" indeterminate></v-progress-circular>
      </div>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { recipeRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useReceipeStore } from "@/stores/receipe";
import { recipeTypes } from "@/constants/list";
import { formatName } from "@/helpers/formatter";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import RecipeTable from "./RecipeTable.vue";
import RecipeItemForm from "./RecipeItemForm.vue";
import TagField from "@/components/fields/TagField.vue";
import HouseUnitField from "@/components/fields/HouseUnitField.vue";
import { truncateNumber } from "@/helpers/money";

const route = useRoute();
const router = useRouter();
const receipeStore = useReceipeStore();
const receipeId = route.params.id;
const isEdit = receipeId !== undefined;

const form = ref(null);
const openForm = ref(false);
const formValid = ref(false);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const ingredients = ref([]);

const loading = ref(false);

const chipColors = [
  "pink-lighten-4",
  "red-lighten-4",
  "green-lighten-4",
  "blue-lighten-4",
  "indigo-lighten-4",
  "purple-lighten-4",
  "purple-lighten-5",
  "amber-lighten-4",
  "cyan-lighten-2",
];

const serviceTypeCards = computed(() => {
  const map = new Map();

  if (!ingredients.value.length) {
    return [{ name: "Total", total: 0 }];
  }

  let totalCost = 0;
  const serviceTypeTotals = {};

  for (const item of ingredients.value) {

    let price = item.unitCost;

    if (item.purchaseUnit?.symbol !== item.recipeUnit?.symbol) {
      if (item.purchaseUnit.toUnit === item.recipeUnit.symbol) {
        price = price / item.purchaseUnit.quantity;
      }
    }

    const cost = truncateNumber(price * item.consumptionQuantity);
    const list = item.serviceType || [];

    if (!list.length) {
      totalCost += cost;
    }

    for (const s of list) {
      const name = s.name;
      serviceTypeTotals[name] = (serviceTypeTotals[name] || 0) + cost;
    }
  }

  const result = [];

  result.push({ name: "Total", total: totalCost });

  for (const [name, ownCost] of Object.entries(serviceTypeTotals)) {
    result.push({
      name,
      total: totalCost + ownCost,
    });
  }

  return result;
});

const getCost = (item) => {
  let cost = item.unitCost;

  if (item.purchaseUnit?.symbol !== item.recipeUnit?.symbol) {
    if (item.purchaseUnit.toUnit === item.recipeUnit.symbol) {
      cost = cost / item.purchaseUnit.quantity;
    }
  }

  return truncateNumber(cost);
};

const add = (data) => {
  ingredients.value.unshift(data);
};

const remove = (index) => {
  ingredients.value.splice(index, 1);
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    if (isEdit) {
      const result = await receipeStore.fetchReceipeById(receipeId);
      record.value = result;
      ingredients.value = record.value.ingredients;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
});

const save = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!ingredients.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }

  loading.value = true;
  try {    
    const payload = {
      ...record.value,
      tags: record.value.tags,
      ingredients: ingredients.value.map((v) => ({
        itemId: v.itemId,
        itemName: v.itemName,
        itemCode: v.itemCode,
        quantity: v.quantity,
        consumptionQuantity: v.consumptionQuantity,
        yield: v.yield,
        serviceType: v.serviceType,
        purchaseUnit: v.purchaseUnit,
        countingUnit: v.countingUnit,
        recipeUnit: v.recipeUnit,
        unitCost: v.unitCost,
        totalCost: truncateNumber(getCost(v) * v.consumptionQuantity),
        isSubRecipe: v.isSubRecipe || false,
      })),
    };

    payload.recipeUnit = {
      id: record.value.recipeUnit.id,
      name: record.value.recipeUnit.name,
      symbol: record.value.recipeUnit.symbol,
      quantity: record.value.recipeUnit?.quantity,
      toUnit: record.value.recipeUnit?.toUnit
    };    

    let itemId;    

    if (isEdit) {
      await receipeStore.updateReceipe(payload);
    } else {
      const createdItem = await receipeStore.createReceipe(payload);
      itemId = createdItem?.id;
    }

    const redirect = (name, id, type, accountId) => {
      router.push({
        name,
        params: { id },
        query: {
          createdItemId: itemId,
          createdItemType: type,
          accountId: accountId,
        },
      });
    };

    const { menuItemId, modifierId, menuItemType, modifierType, accountId } =
      route.query;

    if (menuItemId) {
      return redirect("edit-menu-item", menuItemId, menuItemType, accountId);
    }

    if (modifierId) {
      return redirect("edit-modifier", modifierId, modifierType, accountId);
    }

    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const navigatePrevious = () => {
  const { menuItemId, modifierId, accountId } = route.query;

  if (menuItemId) {
    return router.push({
      name: "edit-menu-item",
      params: { id: menuItemId },
      query: { accountId },
    });
  }

  if (modifierId) {
    return router.push({
      name: "edit-modifier",
      params: { id: modifierId },
      query: { accountId },
    });
  }

  router.push({ name: "recipes" });
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
};
</script>

<style></style>
