<template>
  <div>
    <v-fab
      v-if="!openNav"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.itemType"
                    :items="types"
                    item-title="name"
                    item-value="id"
                    label="Item Type*"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    @update:model-value="onSelectItemType"
                    clearable
                    hide-details="auto"
                    return-object
                    autofocus
                  />
                </v-col>

                <v-col cols="12">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :exclude="excludedItems"
                    :itemFilterOnly="true"  
                    :madeItemOnly="formData.itemType?.id === 'made'"
                    :boughtItemOnly="formData.itemType?.id === 'bought'"                  
                  />
                </v-col>   

                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.quantity"
                    label="Quantity*"
                    type="number"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${formData.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'quantity')"   
                  >
                    <template #append-inner>
                      {{ selectedItem?.recipeUnit?.symbol }}
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.yield"
                    label="Yield*"
                    type="number" 
                    :rules="[rules.min]"                                       
                    variant="outlined"
                    density="compact"
                    color="primary"   
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'yield')"
                    @update:model-value="
                      (val) => {
                        if (val > 1) formData.yield = 1;                      
                      }
                    "  
                  >
                    <template #append-inner>
                      {{ '%' }}
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    :model-value="total"
                    label="consumption Qty"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    readonly
                  />
                </v-col>  

                <v-col cols="12">
                  <v-autocomplete
                    v-model="formData.serviceType"
                    :items="serviceTypes"
                    item-title="name"
                    item-value="id"
                    label="Service Type"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    multiple
                    clearable
                    hide-details="auto"
                    return-object
                    autofocus
                  />
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, watch, computed } from "vue";
import rules from "@/helpers/rules";
import { recipeItemRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  existingItems: { type: Array, default: () => [] },
});

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const selectedItem = ref([]);
const itemField = ref(null);

const serviceTypes = ref([
  { id: "qsr", name: "QSR" },
  { id: "delivery", name: "Delivery" },
  { id: "takeaway", name: "Takeaway" },
  { id: "table", name: "Table" },
  { id: "bar", name: "Bar" },
  { id: "b2b", name: "B2B" },
  // { id: "party", name: "Party" },
]);

const types = ref([
  { id: "bought", name: "Bought" },
  { id: "made", name: "Made" },
]);

const excludedItems = computed(() => {
  const map = {};  
  props.existingItems.forEach((i) => {
    if (!i.itemId) return;

    if (!map[i.itemId]) {
      map[i.itemId] = new Set();
    }
  });
  
  return Object.entries(map).map(([itemId]) => ({
    itemId,
    pkgs: [],
  }));
});

const total = computed(() => { 
  return formData.value?.quantity / formData.value?.yield;
});

const onSelectItemType = async (s) => {  
  selectedItem.value = null;
  formData.value.quantity = null;
};

const onSelectInventoryItem = async (selected) => {      
  selectedItem.value = selected;
  formData.value.itemName = selected.name;
  formData.value.itemCode = selected.code;
  formData.value.itemId = selected.id;
  formData.value.countingUnit = selected.countingUnit;
  formData.value.purchaseUnit = selected.purchaseUnit;
  formData.value.recipeUnit = selected.recipeUnit;
  formData.value.unitCost = selected.unitCostIncludingTax;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value }; 
  payload.consumptionQuantity = total.value;     
  emit("add", payload);

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();
  },
  {
    immediate: true,
  }
);
</script>
