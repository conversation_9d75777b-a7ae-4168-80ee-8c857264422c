<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="recipeList"
          :headers="recipeIngredientHeaders"
          items-per-page="-1"
          fixed-header
          hide-default-footer
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon
              color="error"
              @click="$emit('removeItem', index)"
              @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.rate="{ item }">
            {{ getCost(item) }}
          </template>

          <template #item.total="{ item }">
            {{ getCost(item) * item.consumptionQuantity }}
          </template>

          <template #item.serviceType="{ item }">
            {{ item.serviceType?.length ? item.serviceType.map(s => s.name).join(", ") : "-" }}
          </template>

          <template #no-data>
            <v-btn
              prepend-icon="mdi-plus"
              text="Add Item"
              variant="text"
              border
              rounded="xl"
              @click="$emit('addItem')"
            ></v-btn>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed } from "vue";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import { recipeIngredientHeaders } from "@/helpers/tableHeaders";
import { truncateNumber } from "@/helpers/money";

const props = defineProps({
  recipeList: {
    type: Array,
    default: () => [],
  }
});

const getCost = (item) => {
  let cost = item.unitCost;

  if (item.purchaseUnit?.symbol !== item.recipeUnit?.symbol) {
    if (item.purchaseUnit.toUnit === item.recipeUnit.symbol) {
      cost = cost / item.purchaseUnit.quantity;
    }
  }

  return truncateNumber(cost);
};

const emit = defineEmits(["removeItem", "addItem"]);
</script>
