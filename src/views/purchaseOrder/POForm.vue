<template>
  <div style="height: 100%" v-smart-focus>
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Create GRN"
          :loading="loading"
          :disabled="loading"
          @click="checkSubmit"
          variant="flat"
          color="primary"
          class="ml-2"
        />
      </template>
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-if="isApproved && !isClosed">
        <v-row class="mb-1">
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="cart.goodsReceivedDate"
              label="Goods Received Date"
              color="primary"
              :readonly="isClosed"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
              :min="startDate"
              :max="endDate"
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="cart.vendorInvoiceDate"
              label="Vendor Invoice Date"
              color="primary"
              :readonly="isClosed"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="selectedLocation"
              label="Add Location for direct indent"
              multiple
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              v-model="selectedWorkArea"
              label="Add workarea for direct indent"
              :locations="selectedLocation"
              :multiple="true"
              @update:model-value="addWorkArea"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              class="required-fld"
              v-model="cart.vendorInvoiceNumber"
              label="Invoice No"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[(v) => rules.requireWithName(v, 'Invoice Number')]"
              :readonly="isClosed"
            ></v-text-field>
          </v-col>
          <!-- <v-col cols="12" sm="6" md="3">
            <DirectIssueWorkAreaSelector
              :items="directIssueTableList"
              :headers="directIssueTableHeaders"
              @submit="handleDirectIssueSubmit"
            ></DirectIssueWorkAreaSelector>
          </v-col> -->
        </v-row>
      </v-form>

      <ReasonDialog
        v-if="reasonDialog"
        v-model="reasonDialog"
        @reject="reject"
      ></ReasonDialog>
      <submit-dialog
        v-if="dialog"
        v-model="dialog"
        @selectOption="submit"
      ></submit-dialog>
      <GrnSuccessDialog
        v-if="grnDialog"
        v-model="grnDialog"
        :grn="createdGrn"
        @close="navigatePrevious"
        @view-details="goToDetail"
      />

      <po-overview :data="cart" hide-attachment edit-mode></po-overview>

      <POTable
        ref="poTable"
        :cart="cart"
        :po-list="tableItems"
        :headers="purchaseOrderItemHeaders"
        :selectedWorkArea="selectedWorkAreaDetail"
        @edit="modifyItem"
        @updateQty="updateDirectIssueList"
        @calculate="calculate"
        @copy="duplicateItem"
        @removeItem="removeItem"
      />

      <!-- Drawer Form -->
      <PRItemForm
        v-model="openNav"
        @add="addItem"
        :locationId="selectedLocation[0]"
        :inventoryLocationId="selectedWorkArea[0]"
        :selectedVendorId="cart.vendor.id"
        :currentSelectedItem="selectedItem"
        :showAdd="false"
      />
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import GrnSuccessDialog from "@/views/grn/GrnSuccess.vue";
import ReasonDialog from "@/components/base/ReasonDialog.vue";
import SubmitDialog from "./SubmitDialog.vue";
import POTable from "@/components/purchase/POTable.vue";
// import DirectIssueWorkAreaSelector from "@/components/purchase/DirectIssueWorkAreaSelector.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import PoOverview from "@/components/purchase/PoOverview.vue";
import PRItemForm from "@/components/purchase/PRItemForm.vue";

import { getPurchaseItemHeaders } from "@/helpers/tableHeaders";
import cartHelper from "@/helpers/cartHelper";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import { useLocationStore } from "@/stores/location";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import { useTenantStore } from "@/stores/tenant";
import { useMasterStore } from "@/stores/masterStore";
import { getCurrentMonth } from "@/helpers/date";

const purchaseOrderStore = usePurchaseOrderStore();
const grnStore = useGrnStore();
const locationStore = useLocationStore();
const tenantStore = useTenantStore();
const masterStore = useMasterStore();

const purchaseOrderItemHeaders = ref(
  getPurchaseItemHeaders({
    mode: "po",
    type: "edit",
  })
);

const router = useRouter();
const route = useRoute();

const tableItems = ref([]);
const poTable = ref(null);

const form = ref(null);
const dialog = ref(false);
const reasonDialog = ref(false);
const grnDialog = ref(false);
const createdGrn = ref(null);

const cart = ref(cartHelper.NewCart("po"));

const purchaseOrderId = route.params.id;
const isEdit = purchaseOrderId !== undefined;

const loading = ref(false);

const workAreas = computed(() => locationStore.getLocations || []);
const tenants = computed(() => tenantStore.getTenants);

const defaultSelected = ref([]);
const directIssueList = ref(new Map());

const selectedLocation = ref([]);
const selectedWorkArea = ref([]);

const openNav = ref(false);
const itemOrdinal = ref(0);
const selectedItem = ref(null);

const addWorkArea = (ids) => {
  const selectedIds = ids;
  const isSingle = selectedIds.length === 1;
  const onlyWA = selectedIds[0];

  // =====================================================
  // 🟦 CASE 1: Single WorkArea selected (NORMAL MODE)
  // =====================================================
  if (isSingle) {
    // 1. Remove all dynamic WA columns
    purchaseOrderItemHeaders.value = purchaseOrderItemHeaders.value.filter(
      (h) => h.type !== "edit"
    );

    // 2. Update items
    tableItems.value.forEach((item, index) => {
      if (!item.workAreaQty) item.workAreaQty = {};

      // Remove all except the selected WA
      Object.keys(item.workAreaQty).forEach((waId) => {
        if (waId !== onlyWA) delete item.workAreaQty[waId];
      });

      // Ensure WA exists internally
      if (item.workAreaQty[onlyWA] == null) {
        // ReceivedQty stays as user-entered (default GRN behavior)
        item.workAreaQty[onlyWA] = item.receivedQty || 0;
      }

      // receivedQty stays AS-IS (we DO NOT override user input)
      item.receivedQty = Number(item.receivedQty) || 0;

      // Recalculate line item
      modifyItem(item, index);

      // Direct Issue: Only if WA != inventoryLocation
      const grouped =
        onlyWA === cart.value.inventoryLocation.id
          ? [] // no direct issue
          : [
              {
                workAreaId: onlyWA,
                qty: item.receivedQty,
              },
            ];

      updateDirectIssueList({ item, grouped });
    });

    // 3. Cleanup directIssueList (remove non-selected)
    directIssueList.value.forEach((_, waId) => {
      if (waId !== onlyWA) directIssueList.value.delete(waId);
    });

    return; // STOP — do not run multi-mode logic
  }

  // =====================================================
  // 🟩 CASE 2: Multiple WorkAreas selected (SPLIT MODE)
  // =====================================================

  // 1. Remove old columns
  purchaseOrderItemHeaders.value = purchaseOrderItemHeaders.value.filter(
    (h) => h.type !== "edit" || selectedIds.includes(h.key)
  );

  // 2. Add new WA columns
  selectedWorkAreaDetail.value.forEach((wa) => {
    const exists = purchaseOrderItemHeaders.value.some((h) => h.key === wa.id);
    if (!exists) {
      const insertIndex = purchaseOrderItemHeaders.value.length - 2;
      purchaseOrderItemHeaders.value.splice(insertIndex, 0, {
        key: wa.id,
        title: wa.name,
        align: "center",
        type: "edit",
        minWidth: "150px",
        sortable: false,
      });
    }
  });

  // 3. Recalculate items
  tableItems.value.forEach((item, index) => {
    if (!item.workAreaQty) return;

    // Remove removed WAs
    Object.keys(item.workAreaQty).forEach((waId) => {
      if (!selectedIds.includes(waId)) delete item.workAreaQty[waId];
    });

    // Sum quantities
    const total = Object.values(item.workAreaQty)
      .map((qty) => Number(qty) || 0)
      .reduce((a, b) => a + b, 0);

    item.receivedQty = total;

    modifyItem(item, index);

    const grouped = Object.entries(item.workAreaQty)
      .filter(([_, qty]) => qty > 0)
      .map(([workAreaId, qty]) => ({ workAreaId, qty }));

    updateDirectIssueList({ item, grouped });
  });

  // 4. Clean directIssueList
  directIssueList.value.forEach((entry, waId) => {
    if (!selectedIds.includes(waId)) {
      directIssueList.value.delete(waId);
      return;
    }
    entry.items = entry.items.filter((i) => i.requestedQuantity > 0);
    if (entry.items.length === 0) directIssueList.value.delete(waId);
  });
};

const addItem = (item) => {
  // handle item form close and reset
  selectedItem.value = null;
  openNav.value = false;

  itemOrdinal.value++;

  cart.value = cartHelper.AddItem(cart.value, {
    ...item,
    receivedQty: item.quantity,
    ordinal: itemOrdinal.value,
  });
  tableItems.value = cart.value.items;
};

const removeItem = (index) => {
  cart.value = cartHelper.RemoveItem(cart.value, index);
  tableItems.value = cart.value.items;
};

const duplicateItem = (item) => {
  selectedItem.value = item;
  openNav.value = true;
};

// const otherCharges = computed(() => {
//   return masterStore.getCharges();
// });

const selectedWorkAreaDetail = computed(() => {
  const filtered = workAreas.value.filter((item) =>
    selectedWorkArea.value.includes(item.id)
  );

  return filtered.sort((a, b) => {
    if (a.isDefault && !b.isDefault) return -1;
    if (!a.isDefault && b.isDefault) return 1;
    return 0;
  });
});

const calculate = (charges, taxes) => {
  cart.value = cartHelper.Calculate({ ...cart.value, charges, taxes });
};

const updateDirectIssueList = ({ item, grouped }) => {
  grouped.forEach((wa) => {
    const key = wa.workAreaId; // location key
    const quantity = wa.qty;

    // skip if requester location == issuer location
    if (key === cart.value.inventoryLocation.id) return;

    const existingEntry = directIssueList.value.get(key);

    const newItem = {
      ordinal: item.ordinal,
      itemId: item.itemId,
      itemName: item.itemName,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      requestedQuantity: quantity,
      purchaseUOM: item.purchaseUOM,
      stockable: item.stockable,
      pkg: item.pkg,
    };

    // ******** CASE 1: No entry exists yet ********
    if (!existingEntry) {
      if (quantity > 0) {
        const requesterInfo = selectedWorkAreaDetail.value.find(
          (wa) => wa.id === key
        );
        directIssueList.value.set(key, {
          issuer: {
            id: cart.value.inventoryLocation.id,
            name: cart.value.inventoryLocation.name,
            locationId: cart.value.location.id,
            locationName: cart.value.location.name,
          },
          requester: {
            id: requesterInfo.id,
            name: requesterInfo.name,
            locationId: requesterInfo.locationId,
            locationName: requesterInfo.locationName,
          },
          items: [newItem],
        });
      }
      return;
    }

    // ******** CASE 2: Entry exists ********
    const items = [...existingEntry.items];

    const existingIndex = items.findIndex(
      (i) => i.itemId === newItem.itemId && i.pkg.id === newItem.pkg.id
    );

    if (existingIndex !== -1) {
      if (quantity === 0) {
        // remove item
        items.splice(existingIndex, 1);
      } else {
        // update quantity
        items[existingIndex] = newItem;
      }
    } else if (quantity > 0) {
      // add new item
      items.push(newItem);
    }

    if (items.length > 0) {
      directIssueList.value.set(key, { ...existingEntry, items });
    } else {
      // remove group completely
      directIssueList.value.delete(key);
    }
  });
};

const isApproved = computed(() =>
  cart.value.statusTimeline?.some((item) => item.name === "approved")
);

const isClosed = computed(
  () => cart.value.status === "completed" || cart.value.status === "rejected"
);

const navigatePrevious = () => {
  router.push({ name: "purchase-orders" });
};

const submit = async (type) => {
  loading.value = true;
  const { id, goodsReceivedDate, vendorInvoiceDate, vendorInvoiceNumber } =
    cart.value;

  try {
    const grnItems = [];
    for (const item of tableItems.value) {
      if (item.receivedQty > 0) {
        grnItems.push({
          ordinal: item.ordinal,
          receivedQty: item.receivedQty,
          quantity: item.quantity,
          purchaseUOM: item.purchaseUOM,
          expiryDate: item.expiryDate,
          remarks: item.remarks,
          itemId: item.itemId,
          itemName: item.itemName,
          itemCode: item.itemCode,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          contractPrice: item.contractPrice || 0,
          contractType: item.contractType || null,
          hsnCode: item.hsnCode,
          inclTax: item.inclTax,
          pkg: item.pkg,
          foc: item.foc || false,
          grossAmount: item.grossAmount,
          totalDiscount: item.totalDiscount,
          netAmount: item.netAmount,
          totalChargeAmount: item.totalChargeAmount,
          totalTaxAmount: item.totalTaxAmount,
          totalAmount: item.totalAmount,
          totalCess: item.totalCess,
          totalFocAmount: item.totalFocAmount,
          taxes: item.taxes,
          charges: item.charges,
          unitCost: item.unitCost,
          taxRate: item.taxRate,
          stockable: item.stockable,
        });
      }
    }

    const payload = {
      poId: id,
      grnDate: goodsReceivedDate,
      invoiceDate: vendorInvoiceDate,
      invoiceNumber: vendorInvoiceNumber,
      paymentTerms: cart.value.vendor.paymentTerms,
      poTerms: cart.value.vendor.poTerms,
      remarks: cart.value.remarks,
      grnItems,
      poOption: type,
      grossAmount: cart.value.grossAmount,
      totalDiscount: cart.value.totalDiscount,
      netAmount: cart.value.netAmount,
      totalChargeAmount: cart.value.totalChargeAmount,
      totalTaxAmount: cart.value.totalTaxAmount,
      totalAmount: cart.value.totalAmount,
      totalCess: cart.value.totalCess,
      totalFocAmount: cart.value.totalFocAmount,
      taxes: cart.value.taxes,
      charges: cart.value.charges,
      directIssueList: [...directIssueList.value.values()],
    };

    const response = await grnStore.createGrn(payload);
    createdGrn.value = response?.result;
    grnDialog.value = true;
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};

const checkSubmit = async () => {
  try {
    if (loading.value) return;

    const { valid, errors } = await form.value.validate();
    if (!valid) {
      window.scrollTo({ top: 0, behavior: "smooth" });
      return;
    }

    const groupedTableItem = Object.values(
      tableItems.value.reduce((acc, item) => {
        const key = `${item.itemId}_${item.pkg.id}`;

        if (!acc[key]) {
          acc[key] = {
            ...item,
            receivedQty: item.receivedQty || 0, // or item.quantity
          };
        } else {
          acc[key].receivedQty += item.receivedQty || 0;
        }

        return acc;
      }, {})
    );

    const isAllQuantityMatched = groupedTableItem.every(
      (item) => item.quantity <= item.receivedQty + item.totalReceivedQty
    );

    loading.value = true;

    if (isAllQuantityMatched) {
      await submit(3);
    } else {
      dialog.value = true;
    }
  } finally {
    loading.value = false;
  }
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

// const updatePo = (row) => {
//   const { taxAmount, totalPrice } = calculateTaxAmountAndTotalPrice(row, "po");
//   row.taxAmount = taxAmount >= 0 ? taxAmount : 0;
//   row.totalPrice = totalPrice >= 0 ? totalPrice : 0;
//   return { taxAmount: row.taxAmount, totalPrice: row.totalPrice };
// };

const goToDetail = () => {
  router.push({
    name: "view-grn",
    params: { id: createdGrn.value.id },
  });
};

const startDate = ref(null);
const endDate = ref(null);

const setDateRange = () => {
  if (tenants.value.settings?.monthEndClosing) {
    const monthName = tenants.value.settings?.currentMonth;
    const year = new Date().getFullYear();
    const monthIndex = new Date(`${monthName} 1, ${year}`).getMonth();

    startDate.value = new Date(year, monthIndex, 2)
      .toISOString()
      .substring(0, 10);
    endDate.value = new Date(year, monthIndex + 1, 1)
      .toISOString()
      .substring(0, 10);
  }
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    await locationStore.fetchLocations();
    await tenantStore.fetchTenants();
    setDateRange();

    if (isEdit) {
      const result = await purchaseOrderStore.fetchPurchaseOrderById(
        purchaseOrderId
      );
      cart.value = { ...cartHelper.NewCart("po"), ...result };
      tableItems.value = result.items;
      itemOrdinal.value = tableItems.value[0].ordinal;
      selectedWorkArea.value = [cart.value.inventoryLocation.id];

      const month = tenants.value.settings?.currentMonth;
      cart.value.goodsReceivedDate =
        month === getCurrentMonth()
          ? new Date()
          : endDate.value
          ? new Date(endDate.value)
          : new Date();

      cart.value.vendorInvoiceDate = new Date();

      if (cart.value.status !== "completed") {
        tableItems.value.forEach((item, index) => {
          const ordered = Number(item.quantity) || 0;
          const totalReceived = Number(item.receivedQty) || 0;

          // pending from previous GRNs
          const pending = ordered - totalReceived;
          modifyItem(
            {
              ...item,
              receivedQty: pending > 0 ? pending : 0,
              pendingQty: 0,
              orderedQty: ordered,
              totalReceivedQty: totalReceived,
            },
            index
          );
        });
      }

      if (result.transferNumber) {
        selectedWorkArea.value = [result.transferWorkAreaId];
        addWorkArea([result.transferWorkAreaId]);
      }

      // set defaultSelected
      defaultSelected.value = [cart.value.inventoryLocation.id];

      // set default values
      selectedLocation.value = [cart.value.location.id];
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
</script>
