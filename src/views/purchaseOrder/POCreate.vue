<template>
  <div style="height: 100%" v-smart-focus>
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="close"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Save draft"
          variant="outlined"
          color="primary"
          class="ml-2"
          @click="saveDraft"
          :loading="loading"
          :disabled="loading"
        />
        <v-btn
          text="Submit"
          @click="submitPO"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>

    <!-- Form -->
    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-model="formValid">
        <v-row>
          <v-col v-if="isEdit" cols="12" sm="6" md="3">
            <v-text-field
              v-model="cart.poNumber"
              label="PO Number"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              readonly
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <LocationField
              v-model="cart.location"
              hint="Location creating purchase"
              persistent-hint
              return-object
              mandatory
              hide-details="auto"
              @update:model-value="handleLocationChange"
            ></LocationField>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              ref="waRef"
              v-model="cart.inventoryLocation"
              hint="Workarea creating purchase"
              :locations="cart.location ? [cart.location.id] : null"
              persistent-hint
              return-object
              mandatory
              hide-details="auto"
              :disabled="!cart.location"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <vendor-field
              v-model="cart.vendorId"
              mandatory
              @update:model-value="onChangeVendor"
            ></vendor-field>
          </v-col>
          <v-col cols="12" sm="6" md="3" class="mb-4">
            <v-date-input
              class="required-fld"
              v-model="cart.deliveryDate"
              label="Delivery Date"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
        </v-row>
      </v-form>

      <!-- Overview -->
      <po-overview :data="cart" edit-mode></po-overview>

      <!-- Table -->
      <PRTable
        :cart="cart"
        :pr-list="tableItems"
        :headers="prItemHeaders"
        :formValid="formValid"
        :showVendor="false"
        @removeItem="removeItem"
        @edit="modifyItem"
        @addItem="openForm = true"
      />

      <!-- Drawer Form -->
      <PRItemForm
        v-model="openForm"
        :form-valid="formValid"
        @add="addItem"
        :locationId="cart.location?.id"
        :inventoryLocationId="cart.inventoryLocation?.id"
        :selectedVendorId="cart.vendorId"
        :existingItems="tableItems"
      />
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import PRTable from "@/components/purchase/PRTable.vue";
import PRItemForm from "@/components/purchase/PRItemForm.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import PoOverview from "@/components/purchase/PoOverview.vue";

import { getPurchaseItemHeaders } from "@/helpers/tableHeaders";
import cartHelper from "@/helpers/cartHelper";

import { useVendorStore } from "@/stores/vendor";
import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import { purchaseStatus } from "@/constants/status";

const vendorStore = useVendorStore();
const purchaseOrderStore = usePurchaseOrderStore();

const purchaseRequestItemHeaders = getPurchaseItemHeaders({
  mode: "pr",
  type: "edit",
});

const vendors = computed(() => vendorStore.getVendors);

const router = useRouter();
const route = useRoute();

const tableItems = ref([]);
const openForm = ref(false);
const form = ref(null);
const formValid = ref(false);

const cart = ref(cartHelper.NewCart("pr"));
const purchaseOrderId = route.params.id;
const isEdit = purchaseOrderId !== undefined;
const loading = ref(false);

const itemOrdinal = ref(0);

const prItemHeaders = computed(() => {
  return purchaseRequestItemHeaders.filter((h) => h.key !== "vendor.name");
});

// close form
const close = () => {
  router.push({ name: "purchase-orders" });
};

const waRef = ref(null);

const handleLocationChange = (val) => {
  cart.value.inventoryLocation = null;
  if (val && waRef.value) {
    waRef.value.setDefault(val.inventoryLocationId);
  }
};

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  tableItems.value = cart.value.items;
};

const addItem = (item) => {
  itemOrdinal.value++;

  cart.value = cartHelper.AddItem(cart.value, {
    ...item,
    ordinal: itemOrdinal.value,
  });
  tableItems.value = cart.value.items;
};

const removeItem = (index) => {
  cart.value = cartHelper.RemoveItem(cart.value, index);
  tableItems.value = cart.value.items;
};

const onChangeVendor = (vendorId) => {
  cart.value.vendor = vendors.value.find((vendor) => vendor.id === vendorId);
};

const save = async (status) => {
  openForm.value = false;
  try {
    if (loading.value) return;
    loading.value = true;

    const { valid } = await form.value.validate();
    if (!valid) {
      loading.value = false;
      return;
    }

    const payload = {
      location: {
        id: cart.value.location?.id,
        name: cart.value.location?.name,
      },
      inventoryLocation: {
        id: cart.value.inventoryLocation?.id,
        name: cart.value.inventoryLocation?.name,
      },
      deliveryDate: new Date(cart.value.deliveryDate),
      items: tableItems.value.map((item) => {
        const { vendor, ...rest } = item;
        return rest;
      }),
      vendor: { ...cart.value.vendor },
      remarks: cart.value.remarks,
      status,
      grossAmount: cart.value.grossAmount,
      totalDiscount: cart.value.totalDiscount,
      netAmount: cart.value.netAmount,
      totalChargeAmount: cart.value.totalChargeAmount,
      totalTaxAmount: cart.value.totalTaxAmount,
      totalAmount: cart.value.totalAmount,
      totalCess: cart.value.totalCess,
      totalFocAmount: cart.value.totalFocAmount,
      taxes: cart.value.taxes,
      charges: cart.value.charges,
    };

    if (isEdit) {
      payload.id = cart.value.id;
      payload.prNumber = cart.value.prNumber;
      payload.poNumber = cart.value.poNumber;
      payload.inventoryLocation = {
        id: cart.value.inventoryLocation?.id,
        name: cart.value.inventoryLocation?.name,
      };
      payload.statusTimeline = cart.value.statusTimeline.map((timeline) => ({
        ...timeline,
        time: new Date(timeline.time),
      }));
      await purchaseOrderStore.updatePurchaseOrder(payload);
    } else {
      await purchaseOrderStore.createPurchaseOrder(payload);
    }
    close();
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};

const saveDraft = async () => {
  save(purchaseStatus.DRAFT);
};

const $confirm = inject("confirm");

const submitPO = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  const confirmed = await $confirm(
    "Once you submit, you won’t be able to edit. Are you sure you want to continue?"
  );
  if (!confirmed) return;

  save(purchaseStatus.SUBMITTED);
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    await Promise.all([vendorStore.fetchVendors()]);

    if (isEdit) {
      const result = await purchaseOrderStore.fetchPurchaseOrderById(
        purchaseOrderId
      );
      cart.value = { ...cartHelper.NewCart("pr"), ...result };
      tableItems.value = result.items;
      itemOrdinal.value = tableItems.value[0].ordinal;
      cart.value.vendorId = result.vendor.id;
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
</script>
