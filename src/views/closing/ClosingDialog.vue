<template>
  <v-dialog v-model="model" width="430" persistent>
    <v-card>
      <v-card-title class="d-flex justify-end">
        <v-btn
          variant="tonal"
          icon="mdi-close"
          color="error"
          size="small"
          @click="model = null"
        />
      </v-card-title>

      <v-card-title class="py-2 text-center text-wrap">
        Choose the option for Closing
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4">
        <v-row>
          <v-col
            v-for="option in closingOptions"
            :key="option.id"
            cols="12"
            class="pa-2"
          >
            <v-card
              @click="selectOption(option)"
              class="cursor-pointer"
              :class="{ 'bg-primary': option.selected }"
            >
              <v-card-text>
                <div class="d-flex justify-space-between align-center">
                  <div>
                    <div
                      class="font-weight-medium"
                      v-html="option.message"
                    ></div>
                    <div class="text-caption" v-html="option.description"></div>
                  </div>
                  <v-icon v-if="option.selected" small>mdi-check-circle</v-icon>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex justify-center">
        <v-btn
          color="error"
          variant="text"
          flat
          border
          :disabled="!selectedOption"
          @click="submit"
        >
          Submit
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed } from "vue";
import { ConvertIOStoDate } from "@/helpers/date";

const model = defineModel();
const emit = defineEmits(["selectOption"]);

const props = defineProps({
  closingDate: String,
});

const formattedClosingDate = computed(() => {
  return `<strong>${ConvertIOStoDate(props.closingDate)}</strong>`;
});

const closingOptions = ref([
  {
    id: 1,
    message: `OverWrite Stock for the Closing Date - ${formattedClosingDate.value}`,
    description: `This action updates the stock to the closing stock for ${formattedClosingDate.value}. The live stock will be recalculated based on this update, and the change cannot be undone.`,
    selected: false,
  },
  {
    id: 2,
    message: "Keep Record Only",
    description:
      "The stock will remain unchanged. The uploaded closing stock will be stored for record purposes only.",
    selected: false,
  },
]);

const selectedOption = ref(closingOptions.value.find((opt) => opt.selected));

const selectOption = (option) => {
  closingOptions.value.forEach(
    (item) => (item.selected = item.id === option.id)
  );
  selectedOption.value = option;
};

const submit = () => {
  emit("selectOption", selectedOption.value.id);
  model.value = null;
};
</script>
