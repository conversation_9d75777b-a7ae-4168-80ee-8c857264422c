<template>
  <v-dialog v-model="model" width="500" persistent>
    <v-card>
      <v-card-title class="d-flex justify-end">
        <v-btn
          variant="tonal"
          icon="mdi-close"
          color="error"
          size="small"
          @click="model = null"
        />
      </v-card-title>

      <v-card-title class="py-2 text-center text-wrap">
        Export Closing Data
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4">
        <v-form ref="form" v-model="formValid">
          <v-row>
            <!-- Categories -->
            <v-col cols="12">
              <category-field 
                v-model="filters.categories" 
                label="Categories"
                multiple 
                clearable
                hint="Select categories to filter export data"
                persistent-hint
              />
            </v-col>

            <!-- Sub-Categories -->
            <v-col cols="12">
              <sub-category-field
                v-model="filters.subCategories"
                label="Sub Categories"
                :categories="filters.categories"
                multiple
                clearable
                :disabled="!filters.categories.length"
                hint="Select sub-categories to filter export data"
                persistent-hint
              />
            </v-col>

            <!-- Tags -->
            <v-col cols="12">
              <tag-field 
                v-model="filters.tags" 
                label="Tags"
                multiple 
                clearable
                hint="Select tags to filter export data"
                persistent-hint
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex justify-space-between pa-4">
        <v-btn
          color="error"
          variant="text"
          @click="clearFilters"
        >
          Clear All
        </v-btn>
        
        <div class="d-flex gap-4">
          <v-btn
            variant="outlined"
            color="error"
            class="mr-2"
            @click="handleClose"
          >
            <v-icon icon="mdi-close" start></v-icon>
            <span>Close</span>
          </v-btn>
          <v-btn
            color="primary"
            variant="flat"
            @click="exportData"
            :loading="loading"
          >
            Export Data
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import CategoryField from "@/components/fields/CategoryField.vue";
import SubCategoryField from "@/components/fields/SubCategoryField.vue";
import TagField from "@/components/fields/TagField.vue";

const model = defineModel();
const emit = defineEmits(["export"]);

const props = defineProps({
  locationId: {
    type: [String, Number],
    default: null,
  },
  workAreaId: {
    type: [String, Number],
    default: null,
  },
  closingDate: {
    type: [String, Date],
    default: null,
  },
});

const form = ref(null);
const formValid = ref(true);
const loading = ref(false);

const filters = ref({
  categories: [],
  subCategories: [],
  tags: [],
});

// Clear sub-categories when categories change
watch(
  () => filters.value.categories,
  (newCategories, oldCategories) => {
    if (JSON.stringify(newCategories) !== JSON.stringify(oldCategories)) {
      filters.value.subCategories = [];
    }
  },
  { deep: true }
);

const clearFilters = () => {
  filters.value = {
    categories: [],
    subCategories: [],
    tags: [],
  };
};

const handleClose = () => {
  model.value = null;
};

const exportData = async () => {
  loading.value = true;

  try {
    const exportPayload = {
      type: "data", // Always export data since template is handled separately
      filters: {
        categories: filters.value.categories,
        subCategories: filters.value.subCategories,
        tags: filters.value.tags,
      },
      context: {
        locationId: props.locationId,
        workAreaId: props.workAreaId,
        closingDate: props.closingDate,
      },
    };

    emit("export", exportPayload);
    model.value = null;
  } catch (error) {
    console.error("Export failed:", error);
  } finally {
    loading.value = false;
  }
};
</script>
