<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Submit"
          @click="create"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading || !formValid"
        />

      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-model="formValid">
        <v-row>
          <!-- From Location -->
          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="closingLocation"
              label="Closing Location"
              hint="Closing Location"
              persistent-hint
              mandatory
              return-object
              @update:model-value="locationChange"
            />
          </v-col>

          <!-- From WorkArea -->
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              ref="fromRef"
              v-model="closingWorkArea"
              hint="Closing WorkArea"
              persistent-hint
              mandatory
              return-object
              :locations="closingLocation ? [closingLocation.id] : null"
              :disabled="!closingLocation"
              @update:model-value="workAreaChange"
            />
          </v-col>

          <!-- Closing Date -->
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              class="required-fld"
              v-model="closingDate"
              label="Closing Date"
              color="primary"
              :max="todayDate"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>

          <!-- Import-Export Menu -->
          <v-col cols="12" sm="6" md="3" >
            <closing-import-export-menu
              v-if="closingLocation && closingWorkArea && closingDate"
              :location-id="closingLocation?.id"
              :work-area-id="closingWorkArea?.id"
              :closing-date="closingDate"
              :form-valid="formValid"
              @refresh="refreshData"
              @import-data="handleImportData"
            />
          </v-col>

        </v-row>
      </v-form>

      <!-- Table -->
      <closing-table
        :closingList="tableItems"
        :formValid="formValid"
        @addItem="openForm = true"
        @removeGroup="removeGroup"
        @editGroup="editGroup"
      />

      <closing-item-form
        v-if="closingLocation && closingWorkArea"
        v-model="openForm"
        :formValid="formValid"
        :locationId="closingLocation ? closingLocation.id : null"
        :inventoryLocationId="closingWorkArea ? closingWorkArea.id : null"
        :editItemsData="editItemsData"
        @add="add"
        @update="updateGroup"
        :existingItems="tableItems"
      />

      <closing-dialog
        v-if="dialog"
        v-model="dialog"
        :closing-date="closingDate"
        @selectOption="submit"
      ></closing-dialog>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, inject, watch } from "vue";
import { useRouter } from "vue-router";
import { useClosingStore } from "@/stores/closing";
import { useSnackbarStore } from "@/stores/snackBar";
import rules from "@/helpers/rules";
import ClosingTable from "@/views/closing/ClosingTable.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import LocationField from "@/components/fields/LocationField.vue";
import ClosingItemForm from "@/views/closing/ClosingItemForm.vue";
import ClosingDialog from "./ClosingDialog.vue";
import ClosingImportExportMenu from "./ClosingImportExportMenu.vue";

const router = useRouter();
const { showSnackbar } = useSnackbarStore();
const closingStore = useClosingStore();
const $confirm = inject("confirm");


const openForm = ref(false);
const form = ref(null);
const formValid = ref(false);
const loading = ref(false);

const closingLocation = ref(null);
const closingWorkArea = ref(null);
const closingDate = ref(null);
const stockCorrection = ref(false);

const fromRef = ref(null);
const dialog = ref(false);

const tableItems = ref([]);
const todayDate = new Date().toISOString().split("T")[0];

const add = (data) => {
  tableItems.value.unshift(...data);
};

const removeGroup = async (group) => {
  const itemName = group.value;

  const confirmed = await $confirm(
    "Are you sure you want to delete the item?",
    { title: "Delete Item", confirmText: "Delete" }
  );

  if (!confirmed) return; 

  tableItems.value = tableItems.value.filter(
    item => item.itemName !== itemName
  );
};

const editItemsData = ref(null);

const editGroup = async (group) => {
  const itemName = group.value;

  const groupedItems = tableItems.value.filter(
    (i) => i.itemName === itemName
  );

  editItemsData.value = groupedItems;
  openForm.value = true;
};

const updateGroup = (updatedItems) => {
  if (!updatedItems) return;

  const itemName = updatedItems[0].itemName;

  tableItems.value = tableItems.value.filter(
    (item) => item.itemName !== itemName
  );

  tableItems.value.unshift(...updatedItems);
  openForm.value = false;
};

const handleStockCorrectionToggle = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  // If trying to turn ON the toggle, show confirmation
  if (!stockCorrection.value) {
    const confirmed = await $confirm(
      "Are you sure you want to overwrite the live stock?",
      { title: "Overwrite Live Stock?", confirmText: "Overwrite" }
    );
    if (confirmed) {
      stockCorrection.value = true;
    }
  } else {
    // If turning OFF, no confirmation needed
    stockCorrection.value = false;
  }
};

const submit = async (val) => {    
  if (!tableItems.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }
  loading.value = true;
  try {

    const filteredItems = tableItems.value.filter(i => {
      const hasClosingQty = i.closingQuantity !== null && i.closingQuantity !== "";
      const hasOpenQty = Array.isArray(i.openQuantities) && i.openQuantities.length > 0;

      return hasClosingQty || hasOpenQty;   
    });

    const payload = {
      closingDate: closingDate.value,
      locationId: closingLocation.value?.id,
      locationName: closingLocation.value?.name,
      workAreaId: closingWorkArea.value?.id,
      workAreaName: closingWorkArea.value?.name,
      stockCorrection: val === 1 ? true : false,  
      items: filteredItems.map((i) => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemCode: i.itemCode,
        categoryId: i.categoryId,
        subcategoryId: i.subCategoryId,
        categoryName: i.categoryName,
        subcategoryName: i.subCategoryName,
        closingQuantity: i.closingQuantity ?? 0,
        openQuantities: i.openQuantities,
        countingUOM: i.countingUOM,
        purchaseUOM: i.purchaseUOM,
        recipeUOM: i.recipeUOM,
        pkg: {
          ...i.pkg,
          id: i.pkg?.id,
          name: i.pkg?.name,
        },
      })),
    };

    await closingStore.createClosingData(payload);

    // Clear form data after successful creation
    tableItems.value = [];
    closingLocation.value = null;
    closingWorkArea.value = null;
    closingDate.value = null;
    stockCorrection.value = false;

    navigatePrevious();
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const create = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  dialog.value = true; 
};

const refreshData = () => {
  // Placeholder for refresh functionality
  // This will be implemented in future steps
};

const handleImportData = (transformedItems) => {

  // Clear existing table data and replace with imported items
  if (transformedItems && transformedItems.length > 0) {
    tableItems.value = [...transformedItems]; // Replace existing data
    showSnackbar("success", `Successfully imported ${transformedItems.length} items. Previous data has been cleared.`);
  } else {
    showSnackbar("warning", "No items were imported");
  }
};

const navigatePrevious = () => router.push({ name: "closing" });


const locationChange = async (loc) => {
  // Check if table has items and show confirmation dialog
  if (tableItems.value.length > 0) {
    const confirmed = await $confirm(
      "Changing the location will reset the table data. Do you want to proceed?",
      { title: "Reset Table Data", confirmText: "Yes" }
    );
    if (!confirmed) {
      return; // Do not proceed if user cancels
    }
    // Clear table data if user confirms
    tableItems.value = [];
  }

  closingLocation.value = loc;
  closingWorkArea.value = null;
  if (loc && fromRef.value) {
    fromRef.value.setDefault(loc.inventoryLocationId);
  }
};

const workAreaChange = async (workArea) => {
  // Check if table has items and show confirmation dialog
  if (tableItems.value.length > 0) {
    const confirmed = await $confirm(
      "Changing the location will reset the table data. Do you want to proceed?",
      { title: "Reset Table Data", confirmText: "Yes" }
    );
    if (!confirmed) {
      return; // Do not proceed if user cancels
    }
    // Clear table data if user confirms
    tableItems.value = [];
  }

  closingWorkArea.value = workArea;
}

const focusFirstField = () => {
  // Focus on the first available field (From Location)
  const firstInput = document.querySelector('.v-input input');
  if (firstInput) firstInput.focus();
};

// Reset edit data when form is closed
watch(openForm, (newValue) => {
  if (!newValue) {
    // Form is closed, reset edit data so next open is fresh
    editItemsData.value = null;
  }
});

onMounted(async () => {
  await nextTick();
  focusFirstField();

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

</script>
