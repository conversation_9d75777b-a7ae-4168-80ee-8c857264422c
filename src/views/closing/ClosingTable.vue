<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :group-by="groupBy"
          :items="closingItems"
          :headers="closingTableHeaders"
          items-per-page="-1"
          fixed-header
          hide-default-footer
        >
          <template v-slot:group-header="{ item, columns, toggleGroup, isGroupOpen }">
            <tr>
              <td
                :colspan="columns.length"
                class="cursor-pointer"
                v-ripple
                @click="toggleGroup(item)"
              >
                <div class="d-flex align-center justify-space-between">
                  <div class="d-flex align-center">
                    <v-btn
                      :icon="isGroupOpen(item) ? '$expand' : '$next'"
                      color="medium-emphasis"
                      density="comfortable"
                      size="medium"
                      variant="outlined"
                    ></v-btn>

                    <span class="ms-4">{{ item.value.toUpperCase() }}</span>

                    <v-icon
                      color="primary"
                      class="ms-4"
                      size="small"
                      @click.stop="editGroup(item)"
                    >mdi-pencil-box-outline</v-icon>
                  </div>

                  <div>
                    <v-icon
                      color="error"
                      size="small"
                      @click.stop="removeGroup(item)"
                    >mdi-delete-outline</v-icon>
                  </div>
                </div>
              </td>
            </tr>
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.pkg="{ item }">
            {{ item.pkgName }}
          </template>

          <template #item.closingQuantity="{ item }">
            {{ item.quantity }}
          </template>

          <template #no-data>
            <v-btn
              :disabled="!formValid"
              prepend-icon="mdi-plus"
              text="Add Item"
              variant="text"
              border
              rounded="xl"
              @click="$emit('addItem')"
            ></v-btn>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed } from "vue";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import { closingTableHeaders } from "@/helpers/tableHeaders";

const props = defineProps({
  closingList: {
    type: Array,
    default: () => [],
  },
  formValid: {
    type: Boolean,
    default: false,
  },
});

const groupBy = [{ key: 'itemName', order: 'asc' }]

const emit = defineEmits(["addItem", "removeGroup", "editGroup"]);

const removeGroup = (groupItem) => {
  emit("removeGroup", groupItem);
};

const editGroup = (groupItem) => {
  emit("editGroup", groupItem);
};

const closingItems = computed(() => {
  const pkgRows = [];
  const openRows = [];

  props.closingList.forEach(item => {
    if (item.closingQuantity !== null) {
      pkgRows.push({
        ...item,
        isOpenRow: false,
        pkgName: item.pkg?.name,
        quantity: item.closingQuantity,
      });
    }
    if (item.openQuantities?.length > 0) {
      item.openQuantities.forEach(q => {
        openRows.push({
          ...item,
          isOpenRow: true,
          pkgName: `${item.pkg?.name} (open)`,
          quantity: q
        });
      });
    }
  });

  return [...pkgRows, ...openRows];
});


</script>
