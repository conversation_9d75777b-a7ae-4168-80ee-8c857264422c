<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12" class="mb-2">
                  <inventory-item-field
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :exclude="excludedItems"
                    :showOnly="editItemsData?.length > 0"
                    :itemFilterOnly="true"
                    autofocus
                  />
                </v-col>

                <v-divider v-if="selectedItem && Object.keys(selectedItem).length > 0" class="mb-5"/>

                <!-- PACKAGE SECTION -->
                <span v-if="selectedItem && Object.keys(selectedItem).length > 0" class="ml-3 mb-5 text-medium-emphasis">PACKAGE SECTION</span>

                <div
                  v-for="pkg in packageList"
                  :key="pkg.id"
                  class="px-3"
                >
                  <v-text-field
                    v-model.number="formData.pkgQuantities[pkg.id]"
                    label="Closing Qty"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData.pkgQuantities, pkg.id)"
                  >
                    <template #append-inner>
                      {{ pkg?.name ? pkg.name : selectedItem?.purchaseUnit?.symbol }}
                    </template>
                  </v-text-field>
                </div>

                <v-divider v-if="selectedItem && Object.keys(selectedItem).length > 0" class="mb-5"/>

                <!-- OPEN SECTION -->
                <span v-if="selectedItem && Object.keys(selectedItem).length > 0" class="ml-3 mb-5 text-medium-emphasis">OPEN SECTION</span>

                <div
                  v-for="(pkg, pkgIndex) in openSectionPackages"
                  :key="pkg.id"
                  class="px-3 mb-3"
                >
                  <v-divider
                    v-if="pkgIndex > 0"
                    class="mb-4"
                  />

                  <div class="d-flex align-center mb-2">
                    <v-text-field
                      v-model.number="formData.openQuantities[pkg.id][0]"
                      label="Closing Qty"
                      type="number"
                      variant="outlined"
                      density="compact"
                      color="primary"
                      hide-details="auto"
                    >
                      <template #append-inner>
                        {{ selectedItem?.showWeight ? 'kg' : selectedItem?.countingUnit?.symbol }}
                      </template>
                    </v-text-field>

                    <span class="mx-2 text-body-1"> {{ pkg?.name  }} </span>

                    <v-btn
                      v-if="pkg.id !== 'default'"
                      icon="mdi-plus"
                      color="primary"
                      variant="text"
                      @click="addQty(pkg.id)"
                    />
                  </div>

                  <div
                    v-for="(qty, index) in formData.openQuantities[pkg.id].slice(1)"
                    :key="index"
                    class="d-flex align-center mb-2"
                  >
                    <v-text-field
                      v-model.number="formData.openQuantities[pkg.id][index + 1]"
                      label="Closing Qty"
                      type="number"
                      variant="outlined"
                      density="compact"
                      color="primary"
                      hide-details="auto"
                    >
                      <template #append-inner>
                        {{ selectedItem?.showWeight ? 'kg' : selectedItem?.countingUnit?.symbol }}
                      </template>
                    </v-text-field>

                    <span class="mx-2 text-body-1"> {{ pkg?.name  }} </span>

                    <v-btn
                      icon="mdi-close"
                      color="error"
                      variant="text"
                      @click="removeQty(pkg.id, index + 1)"
                    />
                  </div>
                </div>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, computed, reactive, watch } from "vue";
import { closingRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";
import { useSnackbarStore } from "@/stores/snackBar";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  inventoryLocationId: {
    type: String,
    default: null,
  },
  existingItems: { 
    type: Array, 
    default: () => [] 
  },
  editItemsData: {
    type: Array,
    default: () => null
  }
});

const form = ref();
const openNav = defineModel();
const snackbarStore = useSnackbarStore();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

formData.value = {
  ...DEFAULT_RECORD,
  pkgQuantities: {},
  openQuantities: {}   
};

const addQty = (pkgId) => {
  formData.value.openQuantities[pkgId].push(null);
};

const removeQty = (pkgId, index) => {
  if (index > 0) {
    formData.value.openQuantities[pkgId].splice(index, 1);
  }
};

const packageList = ref([]);
const selectedItem = ref([]);

const openSectionPackages = computed(() =>
  selectedItem.value?.countingUnit?.symbol === selectedItem.value?.purchaseUnit?.symbol
    ? packageList.value.filter(pkg => pkg.id !== "default")
    : packageList.value
);

const excludedItems = computed(() => {
  const map = {};  
  props.existingItems.forEach((i) => {
    if (!i.itemId || !i.pkg?.id) return;

    if (!map[i.itemId]) {
      map[i.itemId] = new Set();
    }

    map[i.itemId].add(i.pkg.id);
  });
  
  return Object.entries(map).map(([itemId, pkgs]) => ({
    itemId,
    pkgs: Array.from(pkgs),
  }));
});

const prefillData = async () => {  
  if (!props.editItemsData || props.editItemsData.length === 0) return;

  const data = props.editItemsData[0];

  formData.value.itemId = data.itemId;
  formData.value.itemName = data.itemName;
  formData.value.itemCode = data.itemCode;
  formData.value.categoryId = data.categoryId;
  formData.value.subCategoryId = data.subCategoryId;
  formData.value.countingUOM = data.countingUOM;
  formData.value.purchaseUOM = data.purchaseUOM;
  formData.value.recipeUOM = data.recipeUOM;
  formData.value.categoryName = data.categoryName;
  formData.value.subCategoryName = data.subCategoryName;

  selectedItem.value = {
    id: data.itemId,
    name: data.itemName,
  };

  packageList.value = props.editItemsData.map(i => ({
    ...i.pkg
  }));

  formData.value.pkgQuantities = {};
  formData.value.openQuantities = {};

  props.editItemsData.forEach((row) => {
    const pkgId = row.pkg.id;

    formData.value.pkgQuantities[pkgId] = row.closingQuantity ?? null;
    formData.value.openQuantities[pkgId] = row.openQuantities.length > 0 ? [...row.openQuantities] : [null];
  });
};

const onSelectInventoryItem = async (selected) => {
  // if (props.editItemsData) return;

  selectedItem.value = selected;

  // Handle case when field is cleared (selected is null)
  if (!selected) {
    packageList.value = [];
    formData.value.pkgQuantities = {};
    formData.value.openQuantities = {};
    return;
  }

  let initialPackages = [...selected.packages];

  const rules = {
    kg:  { toUnit: "kg",  quantity: 1 },
    l:   { toUnit: "l",   quantity: 1 },
    g:   { toUnit: "g",   quantity: 1000 },
    ml:  { toUnit: "ml",  quantity: 1000 },
    nos: { toUnit: "nos", quantity: 1 },
  };

  if (selected.defaultPackage) {
    const data = rules[selected.purchaseUnit?.symbol];

    initialPackages.unshift({
      name: selected.purchaseUnit?.symbol,
      id: "default",
      toUnit: data?.toUnit,
      quantity: data?.quantity,
    });
  }

  packageList.value = initialPackages;

  formData.value.pkgQuantities = {};
  formData.value.openQuantities = {};

  initialPackages.forEach(pkg => {
    formData.value.pkgQuantities[pkg.id] = null;   
    formData.value.openQuantities[pkg.id] = [null]; 
  });

  await getItemDetails(selected, initialPackages);
};

const getItemDetails = async (selected, initialPackages) => {
  const mergedPackages = new Map(
    initialPackages.map(pkg => [pkg.id, pkg])
  );

  packageList.value = [...mergedPackages.values()];
  
  formData.value.itemName = selected.name;
  formData.value.itemCode = selected.code;
  formData.value.itemId = selected.id;
  formData.value.categoryId = selected.categoryId;
  formData.value.categoryName = selected.categoryName;
  formData.value.subCategoryId = selected.subCategoryId;
  formData.value.subCategoryName = selected.subCategoryName;
  formData.value.countingUOM = selected.countingUnit.symbol;
  formData.value.purchaseUOM = selected.purchaseUnit.symbol;
  formData.value.recipeUOM = selected.recipeUnit.symbol;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  // Check if at least one field has a value
  const hasAnyValue = packageList.value.some(pkg => {
    const pkgId = pkg.id;
    const pkgQty = formData.value.pkgQuantities[pkgId];
    const openQty = (formData.value.openQuantities[pkgId] || []).filter(v => v !== null && v !== "");

    // Check if closing quantity has a value
    const hasClosingQty = pkgQty !== null && pkgQty !== undefined && pkgQty !== "";

    // Check if any open quantity has a value
    const hasOpenQty = openQty.length > 0;

    return hasClosingQty || hasOpenQty;
  });

  if (!hasAnyValue) {
    // Show error message and prevent submission
    snackbarStore.showSnackbar("error", "At least one field must contain a value before submission");
    return;
  }

  const payload = [];

  for (const pkg of packageList.value) {
    const pkgId = pkg.id;

    const pkgQty = formData.value.pkgQuantities[pkgId];
    const openQty = (formData.value.openQuantities[pkgId] || []).filter(v => v !== null && v !== "");

    payload.push({
      itemId: formData.value.itemId,
      itemName: formData.value.itemName,
      itemCode: formData.value.itemCode,
      categoryId: formData.value.categoryId,
      subCategoryId: formData.value.subCategoryId,
      countingUOM: formData.value.countingUOM,
      purchaseUOM: formData.value.purchaseUOM,
      recipeUOM: formData.value.recipeUOM,
      pkg: { ...pkg },
      closingQuantity: pkgQty,
      openQuantities: openQty,
      categoryName: formData.value.categoryName,
      subCategoryName: formData.value.subCategoryName,
    });
  }

  if (payload.length === 0) return;

  if (props.editItemsData) {
    emit("update", payload);  
    props.editItemsData.length = 0;  
  } else {
    emit("add", payload);       
  }

  resetFormState();
  smartTab.focusFirst();
};

const blurField = (obj, key) => {
  if (obj[key] === null || obj[key] === undefined || obj[key] === "") return;
  if (obj[key] < 0) obj[key] = 0;
};

// Reset form to initial state
const resetFormState = () => {
  packageList.value = [];
  selectedItem.value = null;
  formData.value = {
    ...JSON.parse(JSON.stringify(DEFAULT_RECORD)),
    pkgQuantities: {},
    openQuantities: {}
  };

  // Reset filters to initial state
  filter.value = {
    categories: [],
    subCategories: [],
  };

  // Reset form validation
  if (form.value) {
    form.value.resetValidation();
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  async (val) => {
    if (val) {
      // Form is opening
      if (props.editItemsData) {
        await prefillData();
      }
      smartTab.focusFirst();
    } else {
      // Form is closing - discard any temporary data and reset completely
      resetFormState();
    }
  },
  { immediate: true }
);

</script>
