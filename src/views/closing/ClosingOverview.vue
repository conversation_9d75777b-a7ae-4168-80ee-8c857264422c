<template>
  <v-card border rounded="lg" class="mb-2">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- Closing Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.closingNumber }}
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="closingDetails" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import LabelValueView from "@/components/utils/LabelValueView.vue";
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const closingDetails = [
  { label: "Closing No.", value: props.data.closingNumber },
  { label: "Location", value: props.data.locationName },
  { label: "Workarea", value: props.data.workAreaName },
  { label: "Created By", value: props.data.closedBy?.userName },
  { label: "Created At", value: props.data.closedBy?.time },
];
</script>
