<template>
  <v-dialog v-model="dialog" max-width="400" persistent>
    <v-card>
      <!-- Title -->
      <v-card-title>
        <v-row align="center" justify="space-between">
          <v-col cols="auto mt-2">
            <span>Import Closing Data</span>
          </v-col>
          <v-col cols="auto">
            <v-btn
              icon="mdi-close"
              variant="text"
              @click="dialog = false"
              color="error"
            ></v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-divider />

      <!-- Body -->
      <v-card-text class="ma-2 pa-0">
        <v-card
          class="d-flex align-start pa-2"
          min-height="80"
          variant="tonal"
          color="primary"
        >
          <v-col cols="auto" class="pa-0">
            <v-icon start color="green" size="40" icon="mdi-file-excel" />
          </v-col>

          <v-col cols="8" class="pa-0">
            <p class="text-black text-subtitle-1">{{ fileName }}</p>
          </v-col>

          <v-spacer />

          <v-col cols="1" class="pa-0 d-flex justify-end">
            <v-progress-circular
              v-if="loading"
              indeterminate
              color="primary"
            />
            <v-icon
              v-else-if="isSuccess"
              icon="mdi-check-circle"
              color="green"
              size="24"
            />
            <v-icon
              v-else
              icon="mdi-attachment"
              color="red"
              size="28"
              class="cursor-pointer"
              @click="$emit('retry')"
            />
          </v-col>
        </v-card>

        <div v-if="errorMessage" class="my-2 text-error">
          <div style="white-space: pre-line;">{{ errorMessage }}</div>
          <div v-if="taskId" class="my-2">
            Task id: {{ taskId }},
            <span
              class="text-red text-decoration-underline cursor-pointer"
              @click="goToLogs"
            >
              Check Logs
            </span>
          </div>
        </div>

        <div v-if="isSuccess && successMessage" class="my-2 text-success">
          {{ successMessage }}
        </div>
      </v-card-text>

      <v-divider />

      <!-- Footer -->
      <v-card-actions class="justify-end">
        <v-btn
          v-if="!isSuccess"
          variant="flat"
          color="primary"
          :disabled="loading"
          @click="onUpload"
        >
          Upload
        </v-btn>

        <v-btn
          v-if="isSuccess"
          variant="flat"
          color="success"
          @click="dialog = false"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { useRouter } from "vue-router";

const dialog = defineModel();

const emit = defineEmits(["upload", "retry"]);

const props = defineProps({
  fileName: { type: String, default: "" },
  errorMessage: { type: String, default: "" },
  successMessage: { type: String, default: "" },
  loading: { type: Boolean, default: false },
  isSuccess: { type: Boolean, default: false },
  taskId: { type: String, default: "" },
});

const router = useRouter();

function goToLogs() {
  if (router.currentRoute.value.path === "/import-export") {
    router.go(); // force reload same page
  } else {
    router.push("/import-export");
  }
}

const onUpload = () => emit("upload");
</script>
