<template>
  <v-container fluid class="px-0">
    <v-card border rounded="lg" width="100%">   
      <v-data-table
          class="table-bordered"
          :group-by="groupBy"
          :items="closingItems"
          :headers="closingTableItemHeaders"
          items-per-page="-1"
          fixed-header
          hide-default-footer
        >
          <template v-slot:group-header="{ item, columns, toggleGroup, isGroupOpen }">
            <tr>
              <td
                :colspan="columns.length"
                class="cursor-pointer"
                v-ripple
                @click="toggleGroup(item)"
              >
                <div class="d-flex align-center">
                  <v-btn
                    :icon="isGroupOpen(item) ? '$expand' : '$next'"
                    color="medium-emphasis"
                    density="comfortable"
                    size="medium"
                    variant="outlined"
                  ></v-btn>

                  <span class="ms-4">{{ item.value.toUpperCase() }}</span>

                </div>
              </td>
            </tr>
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.pkg="{ item }">
            {{ item.pkgName }}
          </template>

          <template #item.closingQuantity="{ item }">
            {{ item.quantity }}
          </template>

        </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { computed } from "vue";
import { closingTableItemHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
});

const groupBy = [{ key: 'itemName', order: 'asc' }]

const closingItems = computed(() => {
  const pkgRows = [];
  const openRows = [];

  props.items.forEach(item => {
    // if (item.closingQuantity !== 0) {
      pkgRows.push({
        ...item,
        isOpenRow: false,
        pkgName: item.pkg?.name,
        quantity: item.closingQuantity,
      });
    // }
    if (item.openQuantities?.length > 0) {
      item.openQuantities.forEach(q => {
        // if (q !== 0) {
          openRows.push({
            ...item,
            isOpenRow: true,
            pkgName: `${item.pkg?.name} (open)`,
            quantity: q
          });
        // }
      });
    }
  });

  return [...pkgRows, ...openRows];
});
</script>
