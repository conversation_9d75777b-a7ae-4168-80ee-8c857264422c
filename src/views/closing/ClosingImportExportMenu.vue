<template>
  <v-menu location="bottom" :close-on-content-click="false">
    <template v-slot:activator="{ props }">
      <v-btn class="ml-2" variant="tonal" color="primary" v-bind="props">
        <v-icon size="x-large" icon="mdi-folder-arrow-up-down"></v-icon>
      </v-btn>
    </template>

    <v-card class="d-flex">
      <v-row no-gutters>
        <!-- ACTION BUTTONS -->
        <v-col cols="12" sm="auto">
          <v-toolbar density="compact" title="Import/Export" />
          <v-divider />
          <v-list>
            <v-list-item
              @click="exportHandler"
              title="Export Closing Data"
              :disabled="!formValid"
            >
              <template v-slot:append>
                <v-list-item-action>
                  <v-icon
                    icon="mdi-folder-arrow-down"
                    :color="formValid ? 'primary' : 'grey'"
                  ></v-icon>
                </v-list-item-action>
              </template>
            </v-list-item>
            <v-divider />
            <v-list-item
              @click="triggerFileInput"
              title="Import Closing Data"
              :disabled="!formValid"
            >
              <template v-slot:append>
                <v-list-item-action>
                  <v-icon
                    icon="mdi-folder-arrow-up"
                    :color="formValid ? 'primary' : 'grey'"
                  ></v-icon>
                </v-list-item-action>
              </template>
            </v-list-item>
          </v-list>
        </v-col>
      </v-row>
    </v-card>
  </v-menu>
  <input
    ref="fileInput"
    type="file"
    style="display: none"
    @change="onFileChange"
    accept=".xlsx,.xls"
  />
  <closing-import-dialog
    v-if="dialog"
    v-model="dialog"
    :file-name="importFileName"
    :error-message="importError"
    :success-message="importSuccessMessage"
    :loading="importUploading"
    :is-success="importSuccess"
    :task-id="taskId"
    @upload="confirmImportUpload"
    @retry="triggerFileInput"
  />
  <closing-export-dialog
    v-if="exportDialog"
    v-model="exportDialog"
    :location-id="locationId"
    :work-area-id="workAreaId"
    :closing-date="closingDate"
    @export="handleExport"
  />
</template>

<script setup>
import { ref, inject } from "vue";
import { useSnackbarStore } from "@/stores/snackBar";
import { useMasterStore } from "@/stores/masterStore";
import * as XLSX from "xlsx";
import ClosingImportDialog from "./ClosingImportDialog.vue";
import ClosingExportDialog from "./ClosingExportDialog.vue";

const props = defineProps({
  locationId: {
    type: [String, Number],
    default: null,
  },
  workAreaId: {
    type: [String, Number],
    default: null,
  },
  closingDate: {
    type: [String, Date],
    default: null,
  },
  formValid: {
    type: Boolean,
    default: false,
  },
});

const fileInput = ref(null);
const importFile = ref(null);
const importFileName = ref("");
const dialog = ref(false);
const exportDialog = ref(false);
const importError = ref(null);
const importSuccessMessage = ref(null);
const taskId = ref(null);
const importUploading = ref(false);
const importSuccess = ref(false);
const $loader = inject("loader");

const snackbarStore = useSnackbarStore();
const masterStore = useMasterStore();

const emit = defineEmits(["refresh", "import-data"]);

// Unit conversion rules for default packages
const unitConversionRules = {
  kg:  { toUnit: "kg",  quantity: 1 },
  l:   { toUnit: "l",   quantity: 1 },
  g:   { toUnit: "g",   quantity: 1000 },
  ml:  { toUnit: "ml",  quantity: 1000 },
  nos: { toUnit: "nos", quantity: 1 },
};

const filterAndTransformData = (exportPayload) => {
  const { filters } = exportPayload;

  // Step 1: Get inventory items from master store using the same method as InventoryItemField.vue
  const inventoryItems = masterStore.getInventoryItems({
    categoryId: filters.categories || [],
    subCategoryId: filters.subCategories || [],
    vendors: [] // We don't have vendor filtering in export dialog, but keeping for consistency
  });

  // Step 2: Filter by tags if specified
  let filteredItems = inventoryItems;
  if (filters.tags && filters.tags.length > 0) {
    filteredItems = inventoryItems.filter(item => {
      return item.menuTags && item.menuTags.some(tag =>
        filters.tags.includes(tag)
      );
    });
  }

  // Step 3: Transform each item into multiple objects based on packages
  const transformedData = [];

  filteredItems.forEach(item => {
    // Process each package in the item
    if (item.packages && item.packages.length > 0) {
      item.packages.forEach(pkg => {
        const packageObject = {
          // Item identification
          itemId: item.id,
          itemName: item.name,
          itemCode: item.code,
          itemSubtitle: item.subtitle,
          hsnCode: item.hsnCode,

          // Category information
          categoryId: item.categoryId,
          subCategoryId: item.subCategoryId,
          categoryName: item.categoryName,
          subCategoryName: item.subCategoryName,

          // Item properties
          stockable: item.stockable,
          showWeight: item.showWeight,
          itemType: item.itemType,
          menuTags: item.menuTags,
          vendors: item.vendors,

          // Unit information
          purchaseUnit: item.purchaseUnit,
          countingUnit: item.countingUnit,
          recipeUnit: item.recipeUnit,
          unitCost: item.unitCost,

          // Package information
          packageId: pkg.id,
          packageName: pkg.name,
          packageQuantity: pkg.quantity,
          packageToUnit: pkg.toUnit,
          packageCode: pkg.packageCode,
          packageUnitCost: pkg.unitCost,
          packageEmptyWeight: pkg.emptyWeight,
          packageFullWeight: pkg.fullWeight,

          // Additional metadata
          isDefaultPackage: false,

          // Closing UOM logic: if isDefaultPackage === false and showWeight === true, then kg, otherwise countingUnit
          closingUOM: (item.showWeight === true) ? 'kg' : item.countingUnit.symbol
        };

        transformedData.push(packageObject);
      });
    }

    // Step 4: If defaultPackage === true, add an extra record using unit conversion rules
    if (item.defaultPackage && item.purchaseUnit) {
      const rule = unitConversionRules[item.purchaseUnit.symbol];
      if (rule) {
        const defaultPackageObject = {
          // Item identification
          itemId: item.id,
          itemName: item.name,
          itemCode: item.code,
          itemSubtitle: item.subtitle,
          hsnCode: item.hsnCode,

          // Category information
          categoryId: item.categoryId,
          subCategoryId: item.subCategoryId,
          categoryName: item.categoryName,
          subCategoryName: item.subCategoryName,

          // Item properties
          stockable: item.stockable,
          showWeight: item.showWeight,
          itemType: item.itemType,
          menuTags: item.menuTags,
          vendors: item.vendors,

          // Unit information
          purchaseUnit: item.purchaseUnit,
          countingUnit: item.countingUnit,
          recipeUnit: item.recipeUnit,
          unitCost: item.unitCost,

          // Default package information (generated from unit conversion rules)
          packageId: "default",
          packageName: item.purchaseUnit.symbol,
          packageQuantity: rule.quantity,
          packageToUnit: rule.toUnit,
          packageCode: null,
          packageUnitCost: null,
          packageEmptyWeight: null,
          packageFullWeight: null,

          // Additional metadata
          isDefaultPackage: true,

          // Closing UOM logic: if isDefaultPackage === false and showWeight === true, then kg, otherwise countingUnit
          // Since this is a default package (isDefaultPackage === true), always use countingUnit
          closingUOM: item.countingUnit.symbol
        };

        transformedData.push(defaultPackageObject);
      }
    }
  });

  return transformedData;
};

const generateExcelFile = (transformedData) => {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // For data export, include the transformed data
  const excelData = transformedData.map(item => ({
    'Category': item.categoryName,
    'Sub-category': item.subCategoryName,
    'Item Code': item.itemCode,
    'Item Name': item.itemName,
    'Package': item.packageName,
    'Closing Qty (Purchase Unit)': '', // Empty for user input
    'Closing UOM': item.closingUOM,
    'Open Qty (Counting Unit)': '' // Empty for user input
  }));

  // Create worksheet from the data
  const worksheet = XLSX.utils.json_to_sheet(excelData);

  // Set column widths for better readability
  const columnWidths = [
    { wch: 20 }, // Category
    { wch: 20 }, // Sub-category
    { wch: 15 }, // Item Code
    { wch: 25 }, // Item Name
    { wch: 15 }, // Package
    { wch: 25 }, // Closing Qty (Purchase Unit)
    { wch: 15 }, // Closing UOM
    { wch: 25 }  // Open Qty (Counting Unit)
  ];
  worksheet['!cols'] = columnWidths;

  // Add the worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Closing Data');

  // Generate filename with local timestamp
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const timestamp = `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;
  const filename = `closing_data_${timestamp}.xlsx`;

  // Write and download the file
  XLSX.writeFile(workbook, filename);

  return filename;
};

function triggerFileInput() {
  if (!props.formValid) return;
  fileInput.value && fileInput.value.click();
}

function onFileChange(event) {
  const file = event.target.files[0];
  if (file) {
    handleImportFile(file);
  }
  // Reset input so same file can be selected again
  event.target.value = "";
}

const exportHandler = async () => {
  if (!props.formValid) return;
  exportDialog.value = true;
};



const handleExport = async (exportPayload) => {
  try {
    $loader.show("Processing export data... Please wait");

    // Filter and transform the data based on user selections
    const transformedData = filterAndTransformData(exportPayload);

    // Check if we have data to export
    if (transformedData.length === 0) {
      snackbarStore.showSnackbar("warning", "No items found matching the selected filters.");
      return;
    }

    // Generate and download Excel file
    const filename = generateExcelFile(transformedData);

    snackbarStore.showSnackbar("success", `Data export completed successfully. File: ${filename}`);

    emit("refresh");
  } catch (error) {
    console.error("Export failed:", error);
    snackbarStore.showSnackbar("error", "Export failed");
  } finally {
    $loader.hide();
  }
};

// Expected column headers for import validation
const EXPECTED_HEADERS = [
  'Category',
  'Sub-category',
  'Item Code',
  'Item Name',
  'Package',
  'Closing Qty (Purchase Unit)',
  'Closing UOM',
  'Open Qty (Counting Unit)'
];

const validateColumnHeaders = (headers) => {
  const errors = [];

  // Check for missing required headers
  const missingHeaders = EXPECTED_HEADERS.filter(expectedHeader => {
    // For "Open Qty (Counting Unit)", it can appear multiple times, so just check if it exists at least once
    if (expectedHeader === 'Open Qty (Counting Unit)') {
      return !headers.some(header => header === expectedHeader);
    }
    // For other headers, they must appear exactly once
    return !headers.includes(expectedHeader);
  });

  if (missingHeaders.length > 0) {
    errors.push(`Missing required column headers: ${missingHeaders.join(', ')}`);
  }

  // Check for unexpected headers (headers not in expected list)
  const unexpectedHeaders = headers.filter(header => {
    // Allow multiple "Open Qty (Counting Unit)" columns
    if (header === 'Open Qty (Counting Unit)') {
      return false;
    }
    return !EXPECTED_HEADERS.includes(header);
  });

  if (unexpectedHeaders.length > 0) {
    errors.push(`Unexpected column headers found: ${unexpectedHeaders.join(', ')}`);
  }

  return errors;
};

const handleImportFile = (file) => {
  resetImportPreview();
  if (!file) return;
  importFile.value = file;
  importFileName.value = file.name || "";

  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      // Parse the Excel file
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });

      // Get the first worksheet
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // Convert to JSON to get headers
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length === 0) {
        importError.value = "The Excel file appears to be empty";
        dialog.value = true;
        return;
      }

      // Get headers from first row
      const headers = jsonData[0] || [];

      // Validate column headers
      const validationErrors = validateColumnHeaders(headers);

      if (validationErrors.length > 0) {
        importError.value = `Column header validation failed:\n\n${validationErrors.join('\n\n')}.\n\nExpected headers: ${EXPECTED_HEADERS.join(', ')}.\n\nNote: "Open Qty (Counting Unit)" can appear multiple times for open quantities.`;
        dialog.value = true;
        return;
      }

      // If validation passes, show dialog for upload
      dialog.value = true;
    } catch (err) {
      const msg = err.message || "Failed to parse file.";
      importError.value = `Failed to parse Excel file: ${msg}`;
      dialog.value = true;
    }
  };
  reader.onerror = () => {
    importError.value = "Failed to read file";
    dialog.value = true;
  };
  reader.readAsArrayBuffer(file);
};

// Step 1: Convert Excel data to internal objects with validation
const convertExcelToInternalObjects = (excelData, headers) => {
  const internalObjects = [];
  const validationErrors = [];

  // Skip header row (index 0)
  for (let i = 1; i < excelData.length; i++) {
    const row = excelData[i];
    if (!row || row.length === 0) continue; // Skip completely empty rows

    const obj = {};
    const rowNumber = i + 1; // Excel row number (1-based)
    const missingColumns = [];

    // Check if this row has any data at all
    const hasAnyData = row.some(cell => cell !== null && cell !== undefined && cell !== '');
    if (!hasAnyData) continue; // Skip completely empty rows

    // Map each column to the corresponding header
    headers.forEach((header, index) => {
      const value = row[index];

      if (header === 'Open Qty (Counting Unit)') {
        // Handle multiple Open Qty columns
        if (!obj.openQuantities) {
          obj.openQuantities = [];
        }
        // Check for empty Open Qty cells
        if (value === null || value === undefined || value === '') {
          // Only report missing if this row has other data
          if (hasAnyData) {
            missingColumns.push(`Open Qty (Counting Unit) - Column ${String.fromCharCode(65 + index)}`);
          }
        } else {
          obj.openQuantities.push(Number(value));
        }
      } else {
        // Map other columns directly
        obj[header] = value;

        // Check for empty Closing Qty cells
        if (header === 'Closing Qty (Purchase Unit)' && hasAnyData) {
          if (value === null || value === undefined || value === '') {
            missingColumns.push(`Closing Qty (Purchase Unit) - Column ${String.fromCharCode(65 + index)}`);
          }
        }
      }
    });

    // Ensure openQuantities is always an array (even if empty)
    if (!obj.openQuantities) {
      obj.openQuantities = [];
    }

    // Check if row has data in Closing Qty or Open Qty
    const hasClosingQty = obj['Closing Qty (Purchase Unit)'] !== null &&
                         obj['Closing Qty (Purchase Unit)'] !== undefined &&
                         obj['Closing Qty (Purchase Unit)'] !== '';
    const hasOpenQty = obj.openQuantities.length > 0;

    // If row has any data but missing required quantity values, report error
    if (hasAnyData && !hasClosingQty && !hasOpenQty) {
      validationErrors.push(`Row ${rowNumber}: Missing values in both Closing Qty (Purchase Unit) and Open Qty (Counting Unit) columns`);
    } else if (hasAnyData && missingColumns.length > 0) {
      // Report specific missing columns for rows that have some data
      validationErrors.push(`Row ${rowNumber}: Missing values in ${missingColumns.join(', ')}`);
    }

    // Only include rows that have data in Closing Qty or Open Qty
    if (hasClosingQty || hasOpenQty) {
      internalObjects.push(obj);
    }
  }

  return { internalObjects, validationErrors };
};

// Step 2: Validate and transform to tableItems structure
const validateAndTransformData = async (internalObjects) => {
  const transformedItems = [];
  const errors = [];

  // Ensure master store data is available
  await masterStore.ensureInventoryItemData();
  const inventoryItems = masterStore.getInventoryItems();

  for (const obj of internalObjects) {
    const itemCode = obj['Item Code'];
    const itemName = obj['Item Name'];
    const packageName = obj['Package'];
    const closingQty = obj['Closing Qty (Purchase Unit)'];

    // Find matching inventory item by both Item Code and Item Name
    const matchedItem = inventoryItems.find(item =>
      item.code === itemCode && item.name === itemName
    );

    if (!matchedItem) {
      errors.push(`No matching item found for Item Code: "${itemCode}" and Item Name: "${itemName}"`);
      continue;
    }

    // Find the matching package
    let matchedPackage = null;

    if (packageName === matchedItem.purchaseUnit.symbol) {
      // This is the default package
      matchedPackage = {
        name: matchedItem.purchaseUnit.symbol,
        id: "default",
        toUnit: matchedItem.purchaseUnit.symbol,
        quantity: 1
      };
    } else {
      // Look for the package in the item's packages array
      matchedPackage = matchedItem.packages?.find(pkg => pkg.name === packageName);

      if (!matchedPackage) {
        errors.push(`No matching package "${packageName}" found for item "${itemName}" (${itemCode})`);
        continue;
      }
    }

    // Create the transformed item structure
    const transformedItem = {
      itemId: matchedItem.id,
      itemName: matchedItem.name,
      itemCode: matchedItem.code,
      categoryId: matchedItem.categoryId,
      subCategoryId: matchedItem.subCategoryId,
      countingUOM: matchedItem.countingUnit.symbol,
      purchaseUOM: matchedItem.purchaseUnit.symbol,
      recipeUOM: matchedItem.recipeUnit.symbol,
      pkg: { ...matchedPackage },
      closingQuantity: closingQty !== null && closingQty !== undefined && closingQty !== '' ? Number(closingQty) : null,
      openQuantities: obj.openQuantities,
      categoryName: matchedItem.categoryName,
      subCategoryName: matchedItem.subCategoryName,
    };

    transformedItems.push(transformedItem);
  }

  return { transformedItems, errors };
};

const confirmImportUpload = async () => {
  if (!importFile.value || importUploading.value) return;

  importUploading.value = true;

  try {
    // Read and parse the Excel file
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        // Parse the Excel file
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON array
        const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        const headers = excelData[0] || [];

        // Step 1: Convert Excel data to internal objects with validation
        const { internalObjects, validationErrors } = convertExcelToInternalObjects(excelData, headers);

        // Check for empty cell validation errors first
        if (validationErrors.length > 0) {
          importError.value = `Data validation failed:\n\n${validationErrors.join('\n')}`;
          importUploading.value = false;
          return;
        }

        // Step 2: Validate and transform the data
        const { transformedItems, errors } = await validateAndTransformData(internalObjects);

        if (errors.length > 0) {
          importError.value = `Data validation failed:\n\n${errors.join('\n')}`;
          importUploading.value = false;
          return;
        }

        if (transformedItems.length === 0) {
          importError.value = "No valid data found in the Excel file. Please ensure you have entered values in either 'Closing Qty (Purchase Unit)' or 'Open Qty (Counting Unit)' columns.";
          importUploading.value = false;
          return;
        }

        // Emit the transformed data to parent component
        importUploading.value = false;
        importSuccess.value = true;
        importSuccessMessage.value = `Import completed successfully. ${transformedItems.length} items processed.`;
        emit("import-data", transformedItems);
        emit("refresh");

      } catch (err) {
        console.error("Import processing error:", err);
        importError.value = `Failed to process import data: ${err.message}`;
        importUploading.value = false;
      }
    };

    reader.onerror = () => {
      importError.value = "Failed to read file";
      importUploading.value = false;
    };

    reader.readAsArrayBuffer(importFile.value);

  } catch (error) {
    console.error("Import error:", error);
    importError.value = `Import failed: ${error.message}`;
    importUploading.value = false;
  }
};

const resetImportPreview = () => {
  importError.value = null;
  importSuccessMessage.value = null;
  importSuccess.value = false;
  taskId.value = null;
};
</script>
