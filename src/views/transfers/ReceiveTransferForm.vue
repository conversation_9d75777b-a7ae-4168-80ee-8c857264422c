<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Export PDF"
          @click="exportPDF"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />

        <v-btn
          v-if="selectedDispatchEntry?.status !== 'completed'"
          text="Receive"
          @click="receiveTransfer"
          variant="flat"
          color="primary"
          class="ml-2"
          :disabled="!enableReceive || loading"
          :loading="loading"
        />
      </template>
    </form-actions-bar>

    <v-container v-if="!loader" fluid>
      <v-row class="mb-1">
        <v-col cols="12" sm="6" md="6">
          <v-autocomplete
            v-model="selectedDispatch"
            :items="dispatchOptions"
            item-value="id"
            item-title="name"
            label="Dispatch Details"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            clearable
          ></v-autocomplete>
        </v-col>
      </v-row>

      <transfer-overview
        :data="record"
        :dispatchDetails="selectedDispatchEntry"
      />

      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-card border rounded="lg" class="my-2" width="100%">
            <v-data-table
              class="table-bordered"
              :headers="receiveTransferHeaders"
              :items="filteredItems"
              items-per-page="-1"
              hide-default-footer
            >
              <template #item.pkgName="{ item }">
                {{ item?.pkg?.name ? item.pkg.name : item.countingUOM }}
              </template>
              
              <template
                #item.receivedQuantity="{ item }"
                v-if="selectedDispatchEntry?.status !== 'completed'"
              >
                <v-text-field
                  v-model.number="item.receivedQuantity"
                  type="number"
                  density="compact"
                  variant="outlined"
                  hide-details
                  color="primary"
                  :min="0"
                  :max="item.dispatchedQuantity"
                  @focus="onFocus(item, 'receivedQuantity')"
                  @blur="onBlur(item, 'receivedQuantity')"
                  @keypress="preventKeys"
                  @update:model-value="
                    (val) => {
                      if (val > item.dispatchedQuantity)
                        item.receivedQuantity = item.dispatchedQuantity;
                      if (val < 0) item.receivedQuantity = 0;
                      item.shortageQuantity =
                        item.dispatchedQuantity - item.receivedQuantity;

                      if (item.shortageQuantity === 0) {
                        item.reason = '';
                      }
                    }
                  "
                >
                  <template v-slot:append-inner>
                    {{
                      item?.pkg.id == "default"
                        ? item.countingUOM
                        : item?.pkg.name
                    }}
                  </template>
                </v-text-field>
              </template>

              <template
                #item.reason="{ item }"
                v-if="selectedDispatchEntry?.status !== 'completed'"
              >
                <v-textarea
                  v-model="item.reason"
                  label="Reason"
                  color="primary"
                  variant="outlined"
                  density="compact"
                  :rows="1"
                  :max-rows="6"
                  hide-details="auto"
                  :disabled="!(item.shortageQuantity > 0)"
                />
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { receiveTransferHeaders } from "@/helpers/tableHeaders";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferOverview from "@/views/transfers/TransferOverview.vue";
import { dateTimeFormat } from "@/helpers/date";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import httpClient from "@/plugin/Axios";

const transferStore = useTransferStore();
const { showSnackbar } = useSnackbarStore();

const router = useRouter();
const route = useRoute();
const transferId = route.params.id;
const dispatchId = route.query.dispatchId;

const record = ref({});
const loader = ref(false);
const loading = ref(false);

const selectedDispatch = ref(null);
const filteredItems = ref([]);

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

const onFocus = (item, value) => {
  if (Number(item[value]) === 0) {
    item[value] = null;
  }
};

const onBlur = (item, value) => {
  if (item[value] === null || item[value] === "") {
    item[value] = 0;
  }
};

const preventKeys = (event) => {
  if (event.key === "-" || event.key === "+" || event.key === "e") {
    event.preventDefault();
  }
};

const enableReceive = computed(() =>
  filteredItems.value.some((item) => item.receivedQuantity > 0)
);

const dispatchOptions = computed(() => {
  if (!record.value.timeLine) return [];
  return record.value.timeLine.map((entry) => ({
    id: entry.dispatchNo,
    name: `${entry.dispatchNo} | ${entry.dispatchedBy.name} | ${dateTimeFormat(
      entry.dispatchedBy.time
    )} | ${entry.status}`.toUpperCase(),
  }));
});

const selectedDispatchEntry = computed(() => {
  if (!record.value.timeLine) return null;
  return record.value.timeLine.find(
    (entry) => entry.dispatchNo === selectedDispatch.value
  );
});

watch(selectedDispatch, (val) => {
  if (!val || !record.value.timeLine || !record.value.items) {
    filteredItems.value = [];
    return;
  }

  const dispatchEntry = record.value.timeLine.find(
    (entry) => entry.dispatchNo === val
  );

  if (!dispatchEntry) {
    filteredItems.value = [];
    return;
  }

  filteredItems.value = dispatchEntry.items.map((dispatchItem) => {
    const originalItem = record.value.items.find(
      (i) =>
        i.itemId === dispatchItem.itemId && i.pkg?.id === dispatchItem.pkg?.id
    );

    return {
      ...dispatchItem,
      itemName: originalItem.itemName,
      itemCode: originalItem.itemCode,
      pkg: dispatchItem.pkg,
      dispatchedQuantity: dispatchItem.dispatchedQuantity,
      receivedQuantity: dispatchItem.dispatchedQuantity,
      shortageQuantity: dispatchItem.shortageQuantity,
      countingUOM: originalItem.countingUOM,
      // reason: dispatchItem.reason,
    };
  });
});

const receiveTransfer = async () => {
  loading.value = true;
  try {
    const hasShortage = filteredItems.value.some(
      (item) => item.shortageQuantity > 0 && !item.reason?.trim()
    );

    if (hasShortage) {
      return showSnackbar("primary", "Provide a valid reason for the shortage");
    }

    const dispatchEntry = record.value.timeLine.find(
      (entry) => entry.dispatchNo === selectedDispatch.value
    );

    const payload = {
      id: record.value.id,
      items: dispatchEntry.items.map((item) => {
        const updated = filteredItems.value.find(
          (i) => i.itemId === item.itemId && i.pkg?.id === item.pkg?.id
        );
        return updated
          ? {
              ...item,
              receivedQuantity: updated.receivedQuantity,
              shortageQuantity: updated.shortageQuantity,
              reason: updated.reason || "",
            }
          : item;
      }),
    };

    const dispatchNumber = dispatchEntry.dispatchNo;
    await transferStore.receiveTransfer(payload, dispatchNumber);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to receive transfer");
  } finally {
    loading.value = false;
  }
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    const dispatchNumber = selectedDispatchEntry.value.dispatchNo;
    await httpClient.get(`transfers/${record.value.id}/pdf`, {
      params: { dispatchNo: dispatchNumber },
      responseType: "blob",
    });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

watch(
  dispatchOptions,
  (options) => {
    if (!options.length) return;

    selectedDispatch.value = dispatchId ? dispatchId : options[0].id;
  },
  { immediate: true }
);

onBeforeMount(async () => {
  loader.value = true;
  try {
    const result = await transferStore.fetchTransferById(transferId, "receive");
    record.value = result;
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});
</script>

<style scoped></style>
