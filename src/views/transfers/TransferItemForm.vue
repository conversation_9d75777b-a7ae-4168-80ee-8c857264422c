<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel v-model="filter" :no-vendor="true" />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    persistent-hint
                    :hint="`HSN Code: ${formData.hsnCode || '-'}`"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :exclude="excludedItems"
                    autofocus
                  />
                </v-col>

                <v-col cols="12" v-if="packageList.length">
                  <v-autocomplete
                    class="required-fld"
                    v-model="formData.pkg"
                    :items="filteredPackages"
                    item-title="name"
                    item-value="id"
                    label="Package/UOM"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="handleInventoryPkg"
                    :disabled="packageList.length === 1"
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.requestedQuantity"
                    label="Quantity"
                    type="number"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${formData.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(formData, 'requestedQuantity')"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, computed, reactive, watch } from "vue";
import rules from "@/helpers/rules";
import { transferRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";

// components
import InventoryFiltersPanel from "@/components/purchase/InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: null,
  },
  inventoryLocationId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
});

const inventoryStore = useInventoryItemStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packageList = ref([]);
const selectedItem = ref([]);

const filteredPackages = computed(() => {
  const item = selectedItem.value;
  if (!item || !item.id) return [];

  const chosenPkgIds = props.existingItems
    .filter((i) => i.itemId === item.id)
    .map((i) => i.pkg?.id);

  return packageList.value.filter((pkg) => !chosenPkgIds.includes(pkg.id));
});

const excludedItems = computed(() => {
  const map = {};

  props.existingItems.forEach((i) => {
    if (!i.itemId || !i.pkg?.id) return;

    if (!map[i.itemId]) {
      map[i.itemId] = new Set();
    }

    map[i.itemId].add(i.pkg.id);
  });

  return Object.entries(map).map(([itemId, pkgs]) => ({
    itemId,
    pkgs: Array.from(pkgs),
  }));
});

watch(filteredPackages, (list) => {
  if (list.length > 0) {
    formData.value.pkg = list[0];
    handleInventoryPkg(formData.value.pkg);
  } else {
    formData.value.pkg = null;
  }
});

const onSelectInventoryItem = async (selected) => {
  selectedItem.value = selected;

  // build package list
  // packageList.value = [
  //   { name: `${selected.purchaseUnit} (default)`, id: "default" },
  //   ...selected.packages,
  // ];

  // build package list
  packageList.value = [...selected.packages];

  if (selected.defaultPackage) {
    packageList.value.unshift({
      name: `${selected.purchaseUnit?.symbol}`,
      id: "default",
    });
  }

  // pkg will be auto-updated by the filteredPackages watcher
};

const handleInventoryPkg = async (v) => {
  if (!v) return;
  const item = await inventoryStore.fetchItemDetails(
    {
      id: selectedItem.value.id,
      locationId: props.locationId,
      inventoryLocationId: props.inventoryLocationId,
      pkgId: v.id,
    },
    false
  );

  formData.value.itemName = item.itemName;
  formData.value.itemCode = item.itemCode;
  formData.value.itemId = item.id;
  formData.value.categoryId = item.category?.id;
  formData.value.categoryName = item.category?.name;
  formData.value.subCategoryId = item.subCategory?.id;
  formData.value.subCategoryName = item.subCategory?.name;
  formData.value.hsnCode = item.hsnCode;
  formData.value.unitCost = resolveUnitCost(item, v);
  formData.value.inStock = item.inStock;
  formData.value.requestedQuantity = item.requestedQuantity;
  formData.value.purchaseUOM =
    v.id === "default" ? item.purchaseUnit.symbol : v.name;
  formData.value.inStock = item.inStock;
  formData.value.inclTax = item.inclTax || false;
  formData.value.stockable =
    item.stockable !== undefined ? item.stockable : false;
};

const resolveUnitCost = (item, v) => {
  if (item.lastGrnPriceIncludingTax) return item.lastGrnPriceIncludingTax;

  if (v && v.id !== "default") {
    const pkg = item.packages.find((p) => p.id === v.id);
    if (pkg) {
      const taxRate = item.taxes.reduce((acc, tax) => {
        if (tax.valueType == "percentage") {
          return acc + tax.valuePercentage;
        }
        return acc;
      }, 0);
      return pkg.unitCost + pkg.unitCost * (taxRate / 100);
    }
  }

  return item.unitCostIncludingTax;
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_RECORD));
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "requestedQuantity") {
    if (item.requestedQuantity < 0 || !item.requestedQuantity) {
      item.requestedQuantity = 0;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();
  },
  {
    immediate: true,
  }
);
</script>
