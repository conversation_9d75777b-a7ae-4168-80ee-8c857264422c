<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #custom>
        <span class="text-subtitle-1">#{{ record.transferNumber }}</span>
      </template>
      <template #actions>
        <transfer-options :item="record" />
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <transfer-overview :data="record" />

      <v-container fluid class="pa-0">
        <v-row no-gutters>
          <v-card border rounded="lg" class="my-2" width="100%">
            <v-data-table
              class="table-bordered"
              :headers="viewTransferHeaders"
              :items="record.items"
              items-per-page="-1"
              hide-default-footer
            >
              <template #item.pkgName="{ item }">
                {{ item?.pkg?.name ? item.pkg.name : item.countingUOM }}
              </template>

              <template #item.dispatchedQuantity="{ item }">
                {{ item.dispatchedQuantity ? item.dispatchedQuantity : 0 }}
              </template>

              <template #item.receivedQuantity="{ item }">
                {{ item.receivedQuantity ? item.receivedQuantity : 0 }}
              </template>
            </v-data-table>
          </v-card>
        </v-row>
      </v-container>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { viewTransferHeaders } from "@/helpers/tableHeaders";
import TransferOptions from "@/views/transfers/TransferOptions.vue";
import TransferOverview from "@/views/transfers/TransferOverview.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";

const transferStore = useTransferStore();
const router = useRouter();
const route = useRoute();

const transferId = route.params.id;

const record = ref({});
const loading = ref(false);

const itemOrdinal = ref(0);

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    const result = await transferStore.fetchTransferById(transferId, "view");
    record.value = result;
    itemOrdinal.value = record.value.items[0].ordinal;
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px);
  padding-right: 16px;
}
</style>
