<template>
  <v-card border rounded="lg" class="mb-2">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- Transfer Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.transferNumber }}
          <div class="pl-2">
              <v-icon v-if="!data.stockableItems" icon="mdi-fruit-cherries" color="error"/>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="details" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
import LabelValueView from "@/components/utils/LabelValueView.vue";
import { dateTimeFormat } from "@/helpers/date";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const transferDetails = [
  { label: "Transfer No.", value: props.data.transferNumber },
  { label: "Requester", value: props.data.requester?.name },
  { label: "Issuer", value: props.data.issuer?.name },
  { label: "Requested By", value: props.data.requestedBy?.name },
  { label: "Requested At", value: props.data.requestedBy?.time },
];

const details = computed(() =>
  transferDetails.filter((detail) => detail.value)
);
</script>
