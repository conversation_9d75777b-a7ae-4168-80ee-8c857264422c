export const vendorRecord = {
  vendorId: "",
  name: "",
  contactEmailId: "",
  contactName: "",
  contactNo: "",
  address: { country: "", address: "", city: "", state: "", pincode: "" },
  cinNo: "",
  gstNo: "",
  panNo: "",
  tinNo: "",
  poTerms: "",
  paymentTerms: "",
};

export const categoryRecord = {
  name: "",
  tenantId: "",
  subCategories: [],
};

export const locationRecord = {
  storeId: "",
  name: "",
};

export const importExportLogRecord = {
  accountId: "",
  type: "",
  options: "",
  datetime: "",
};

export const houseUnitRecord = {
  name: null,
  symbol: null,
  quantity: null,
  toUnit: null,
};

export const taxRecord = {
  name: null,
  valueType: "percentage",
  valueAmt: 0,
  valuePercentage: 0,
  taxLevel: "item",
};

export const chargeRecord = {
  name: null,
  valueType: "percentage",
  valueAmt: 0,
  valuePercentage: 0,
};

export const conversionRecord = {
  quantity: "",
  name: "",
};

export const inventoryRecord = {
  itemType: "bought", // bought/madeItem
  itemName: "",
  itemCode: "",
  tags: [],
  taxes: [],
  category: null,
  subCategory: null,
  purchaseUnit: null,
  countingUnit: null,
  recipeUnit: null,
  parLevel: 0,
  trackExpiry: false,
  packages: [],
  vendors: [],
  activeStatus: true,
  unitCost: 0,
  hsnCode: "",
  stockable: true,
  ledger: null,
  defaultPackage: true,
  showPackage: false,
  showWeight: false,
  allVendors: false,
};

export const packageRecord = {
  name: "",
  packageCode: "",
  quantity: null,
  unitCost: 0,
  emptyWeight: 0,
  fullWeight: 0,
};

export const ingredientRecord = {
  id: "",
  name: "",
  quantity: null,
  recipeUnit: null,
  countingUnit: null,
  purchaseUnit: null,
};

export const recipeRecord = {
  name: "",
  quantity: 0,
  cost: 0,
  activeStatus: true,
  cookingProcedure: [],
  ingredients: [],
  recipeCode: "",
};

export const recipeItemRecord = {
  itemType: null,
  itemName: "",
  itemCode: "",
  itemId: null,
  countingUOM: null,
  purchaseUOM: null,
  recipeUOM: null,
  unitCost: 0,
  quantity: 0,
  yield: 1,
  ingredients: [],
};

export const storeRecord = {
  name: "",
  posId: "",
  locationType: "CENTRAL KITCHEN",
  accountId: "",
  accountName: "",
  tenantId: "",
  billTo: "",
  shipTo: "",
  panNo: "",
  gstNo: "",
};

export const purchaseRequestRecord = {
  location: null,
  inventoryLocation: null,
  items: [],
  prNumber: null,
  vendorType: 2,
  deliveryDate: new Date(),
  vendor: null,
};

export const purchaseOrderRecord = {
  location: null,
  items: [],
  deliveryDate: new Date(),
  vendor: null,
  remarks: null,
  totalExclusiveTax: 0,
  totalTaxAmount: 0,
  totalAmount: 0,
  totalDiscount: 0,
  totalFocAmount: 0,
};

export const purchaseRequestItemRecord = {
  itemId: null,
  itemName: null,
  itemCode: null,
  categoryId: null,
  subcategoryId: null,
  hsnCode: null,
  vendor: null,
  pkg: null,
  purchaseUOM: null,
  remarks: null,
  quantity: 1,
  unitCost: 0,
  tax: [],
  taxRate: 0,
  inStock: 0,
  grossAmount: 0,
  totalDiscount: 0,
  netAmount: 0,
  totalChargeAmount: 0,
  totalTaxAmount: 0,
  totalAmount: 0,
  totalCess: 0,
  totalFocAmount: 0,
};

export const purchaseOrderItemRecord = {
  itemId: "",
  itemName: "",
  itemCode: "",
  quantity: null,
  receivedQuantity: 0,
  purchaseUOM: null,
  unitCost: 0,
  taxRate: 0,
  totalPrice: 0,
};

export const menuItemRecord = {
  id: "",
  tenantId: "",
  itemName: "",
  itemCode: "",
  itemType: "",
  activeStatus: true,
  // groups: [],
  account: { name: "", id: "", posId: "" },
  posId: "",
  servingLevels: [],
};

export const indentRequestRecord = {
  requester: null,
  issuer: null,
  location: null,
  selectedLocation: null,
  items: [],
  requestedBy: null,
};

export const indentIssueRecord = {
  location: null,
  selectedLocation: null,
  items: [],
  requestedBy: null,
};

export const contractRecord = {
  name: "",
  reference: "",
  vendor: null,
  location: [],
  inventoryLocation: null,
  startDate: null,
  endDate: null,
};

export const contractItemRecord = {
  itemId: "",
  itemName: "",
  itemCode: "",
  pkg: null,
  contractType: "fixed",
  contractPrice: null,
  inclTax: true,
};

export const adjustmentRecord = {
  location: null,
  inventoryLocation: null,
};

export const adjustmentItemRecord = {
  itemId: null,
  itemName: null,
  itemCode: null,
  unitCost: 0,
  pkg: null,
};

export const transferRecord = {
  itemId: "",
  itemName: "",
  itemCode: "",
  categoryId: "",
  subCategoryId: "",
  requestedQuantity: null,
  countingUOM: "",
  pkg: null,
};

export const closingRecord = {
  itemId: "",
  itemName: "",
  itemCode: "",
  countingUOM: "",
  categoryId: "",
  subCategoryId: "",
  closingQuantity: null,
};

export const spoilageItemRecord = {
  itemId: null,
  itemName: null,
  itemCode: null,
  unitCost: 0,
  pkg: null,
};

export const reportGroupRecord = {
  name: "",
  category: [],
  workarea: [],
  department: null,
};

