<template>
  <v-menu location="bottom" :close-on-content-click="false">
    <template v-slot:activator="{ props }">
      <v-btn class="ml-2" variant="tonal" color="primary" v-bind="props">
        <v-icon size="x-large" icon="mdi-folder-arrow-up-down"></v-icon>
      </v-btn>
    </template>

    <v-card class="d-flex">
      <v-row no-gutters>
        <!-- LEFT SIDE: SELECT SHEETS -->
        <v-col v-if="!props.sheets" cols="12" sm="auto">
          <v-toolbar density="compact" title="Select Sheets" />
          <v-divider />
          <v-list>
            <div v-for="(option, key) in exportOptions" :key="key">
              <v-list-item :title="option.label">
                <template v-slot:prepend>
                  <v-list-item-action start>
                    <v-switch
                      density="compact"
                      hide-details
                      inset
                      base-color="error"
                      color="success"
                      v-model="option.checked"
                    ></v-switch>
                  </v-list-item-action>
                </template>
              </v-list-item>
              <v-divider />
            </div>
          </v-list>
        </v-col>

        <!-- VERTICAL DIVIDER -->
        <v-divider v-if="!props.sheets" vertical />

        <!-- RIGHT SIDE: ACTION BUTTONS -->
        <v-col cols="12" sm="auto">
          <v-toolbar density="compact" title="Import/Export" />
          <v-divider />
          <v-list>
            <v-list-item @click="exportTemplate" title="Export Template">
              <template v-slot:append>
                <v-list-item-action>
                  <v-icon
                    icon="mdi-folder-arrow-down-outline"
                    color="primary"
                  ></v-icon>
                </v-list-item-action>
              </template>
            </v-list-item>
            <v-divider />
            <v-list-item @click="exportHandler" title="Export">
              <template v-slot:append>
                <v-list-item-action>
                  <v-icon icon="mdi-folder-arrow-down" color="primary"></v-icon>
                </v-list-item-action>
              </template>
            </v-list-item>
            <v-divider />
            <v-list-item @click="triggerFileInput" title="Import">
              <template v-slot:append>
                <v-list-item-action>
                  <v-icon icon="mdi-folder-arrow-up" color="primary"></v-icon>
                </v-list-item-action>
              </template>
            </v-list-item>
          </v-list>
        </v-col>
      </v-row>
    </v-card>
  </v-menu>
  <input
    ref="fileInput"
    type="file"
    style="display: none"
    @change="onFileChange"
    accept=".xlsx,.xls"
  />
  <import-dialog
    v-if="dialog"
    v-model="dialog"
    :file-name="importFileName"
    :sheet-name="sheets"
    :error-message="importError"
    :success-message="importSuccessMessage"
    :loading="importUploading"
    :is-success="importSuccess"
    :task-id="taskId"
    @upload="confirmImportUpload"
    @retry="triggerFileInput"
  />
</template>

<script setup>
import { ref, inject } from "vue";
import { handleExport } from "@/services/exportService";
import { useSnackbarStore } from "@/stores/snackBar";
import importDialog from "@/components/base/importDialog.vue";
import { importSheetData } from "@/services/importService";

const props = defineProps({
  sheets: {
    type: String,
    default: "",
  },
});

const fileInput = ref(null);
const importFile = ref(null);
const importFileName = ref("");
const dialog = ref(false);
const importError = ref(null);
const importSuccessMessage = ref(null);
const taskId = ref(null);
const importUploading = ref(false);
const importSuccess = ref(false);
const $loader = inject("loader");

const snackbarStore = useSnackbarStore();

const emit = defineEmits(["refresh"]);

function triggerFileInput() {
  fileInput.value && fileInput.value.click();
}

const exportOptions = ref({
  tags: { label: "Tags", checked: true, key: "Tags" },
  taxes: { label: "Taxes", checked: true, key: "Taxes" },
  // dynamicTaxes: { label: "Charges", checked: true, key: "Charges" },
  vendors: { label: "Vendors", checked: true, key: "Vendors" },
  categories: {
    label: "Categories & Sub Categories",
    checked: true,
    key: "Categories",
  },
  houseUnits: { label: "House Units", checked: true, key: "House Units" },
  inventoryItems: {
    label: "Inventory Items & Packages",
    checked: true,
    key: "Inventory Items",
  },
  recipes: { label: "Recipes & Ingredients", checked: true, key: "Recipes" },
});

function onFileChange(event) {
  const file = event.target.files[0];
  if (file) {
    handleImportFile(file);
  }
  // Reset input so same file can be selected again
  event.target.value = "";
}

function getSelected() {
  return props.sheets
    ? [props.sheets] // If parent sent a sheet, use that
    : Object.values(exportOptions.value) // Else use local checkboxes
        .filter((opt) => opt.checked)
        .map((opt) => opt.key);
}

const exportHandler = async () => {
  try {
    $loader.show("Downloading... Please wait");
    const selectedSheets = getSelected(); // Get selected sheets here
    await handleExport(false, selectedSheets);
  } catch (error) {
    console.error("Export failed:", error);
  } finally {
    $loader.hide();
  }
};

const exportTemplate = async () => {
  try {
    $loader.show("Downloading... Please wait");
    const selectedSheets = getSelected();
    await handleExport(true, selectedSheets);
  } catch (error) {
    console.error("Export template failed:", error);
  } finally {
    $loader.hide();
  }
};

const handleImportFile = (file) => {
  resetImportPreview();
  if (!file) return;
  importFile.value = file;
  importFileName.value = file.name || "";

  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      dialog.value = true;
    } catch (err) {
      const msg = err.message || "Failed to parse file.";
      snackbarStore.showSnackbar("error", msg);
    }
  };
  reader.onerror = () =>
    snackbarStore.showSnackbar("error", "Failed to read file.");
  reader.readAsArrayBuffer(file);
  console.log("done");
};

const confirmImportUpload = () => {
  if (!importFile.value || importUploading.value) return;

  const selectedSheets = getSelected();

  importUploading.value = true;
  importSheetData({
    file: importFile.value,
    sheetName: selectedSheets,
  })
    .then((res) => {
      const data = res.data;
      let totalInserted = 0;
      let totalFailed = 0;
      let hasFailures = false;

      if (data.failed) {
        Object.keys(data.failed).forEach((module) => {
          if (
            data.failed[module] &&
            Array.isArray(data.failed[module]) &&
            data.failed[module].length > 0
          ) {
            hasFailures = true;
            totalFailed += data.failed[module].length;
          }
        });
      }

      if (data.inserted) {
        Object.keys(data.inserted).forEach((module) => {
          if (data.inserted[module] && Array.isArray(data.inserted[module])) {
            totalInserted += data.inserted[module].length;
          }
        });
      }

      if (hasFailures) {
        snackbarStore.showSnackbar(
          "error",
          `Import finished. Success: ${totalInserted}, Failed: ${totalFailed}. Check Logs.`,
          100000
        );
      } else {
        snackbarStore.showSnackbar(
          "green",
          data.message || "Imported successfully!"
        );
      }
      // resetImportPreview();
      importSuccessMessage.value = data.message;
      taskId.value = data.taskId;
      importSuccess.value = true;
      emit("refresh");
    })
    .catch((err) => {
      const msg =
        err.response?.data?.message || err.message || "Import failed.";
      importError.value = msg;
      taskId.value = err.response?.data?.taskId;
      snackbarStore.showSnackbar("error", msg, 100000);
    })
    .finally(() => {
      importUploading.value = false;
    });
};

const resetImportPreview = () => {
  importFileName.value = "";
  importSuccessMessage.value = "";
  importError.value = "";
  importFile.value = null;
  importSuccess.value = false;
};
</script>

<style scoped></style>
