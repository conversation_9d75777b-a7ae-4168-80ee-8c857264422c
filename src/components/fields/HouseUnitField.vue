<template>
  <auto-complete
    v-model="model"
    :items="filteredHouseUnits"
    v-bind="$attrs"
    :label="label"
    item-title="name"
    item-value="id"
    :hint="conversionHint"
    persistent-hint
    :loading="loading"
    :filter-keys="['title', 'raw.symbol']"
  >
    <template v-slot:item="{ props, item }">
      <v-list-item
        v-bind="props"
        :title="`${item.raw.name} (${item.raw.symbol})`"
      >
      </v-list-item>
    </template>
    <template #selection="{ item }">
      {{ item.raw.name }} ({{ item.raw.symbol }})
    </template>

    <!-- <template #prepend-inner>
      <span v-if="countingUnit?.conversion" class="d-flex"
        >{{ countingUnit?.conversion }}&nbsp; <span>x</span>
      </span>
    </template> -->
  </auto-complete>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";
import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "House Units",
  },
  /** Component-specific attributes */
  purchaseUnit: {
    type: Object,
    default: () => null,
  },
  parentUnit: {
    type: Object,
    default: () => null,
  },
  showConversion: {
    type: Boolean,
    default: false,
  },
  standardUnitsOnly: {
    type: Boolean,
    default: false,
  },
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const houseUnits = computed(() => {
  return masterStore.getHouseUnits({});
});

const filteredHouseUnits = computed(() => {
  let list = houseUnits.value;

  if (props.parentUnit?.symbol) {
    list = getConversionChain(props.parentUnit?.symbol);
  }

  const grouped = [];
  const custom = [];
  const standard = [];

  list.forEach((u) => {
    if (u.default) return standard.push(u);
    if (!props.standardUnitsOnly) custom.push(u);
  });

  if (standard.length) {
    grouped.push({ type: "subheader", name: "STANDARD UNIT" }, ...standard);
  }

  if (custom.length) {
    grouped.push(
      {
        type: "divider",
        text: "or",
      },
      { type: "subheader", name: "HOUSE UNIT" },
      ...custom
    );
  }

  return grouped;
});

/**
 * Builds a conversion chain starting from a given unit symbol.
 * Example: "kg" → "g" → "mg"
 *
 * @param {string} startSymbol - The unit symbol to start tracing from.
 * @returns {Array} An ordered list of unit objects representing the conversion steps.
 */
const getConversionChain = (startSymbol) => {
  // Create a map for fast lookup: { symbol → unitObject }
  const unitMap = {};
  houseUnits.value.forEach((u) => {
    unitMap[u.symbol] = u;
  });

  const chain = []; // Final ordered result chain
  const visited = new Set(); // Prevent infinite loops in circular conversions

  let current = startSymbol;

  // Traverse the chain until no next unit exists OR a loop is detected
  while (current && !visited.has(current)) {
    visited.add(current);

    const unit = unitMap[current];
    if (!unit) break; // Stop if symbol not found in unitMap

    chain.push(unit); // Add current unit to the chain

    // Move to the next "toUnit" in the conversion (e.g., kg → g)
    current = unit.toUnit;
  }

  return chain;
};

const getConversionFactor = computed(() => {
  const symbol = model.value?.symbol;
  const purchase = props.purchaseUnit;
  const units = houseUnits.value; // full list of all units

  if (!purchase) return 1;

  // If it's the same as purchase unit
  if (purchase.symbol === symbol) {
    return 1;
  }

  let factor = 1;
  let current = purchase;

  // Walk down the conversion chain until we reach target symbol
  while (current && current.toUnit) {
    factor *= current.quantity;

    // Reached the target unit
    if (current.toUnit === symbol) {
      return factor;
    }

    // Move to the next unit in the chain
    current = units.find((u) => u.symbol === current.toUnit);
  }

  // If no chain matches, fallback
  return 1;
});

const conversionHint = computed(() => {
  if (!props.showConversion || getConversionFactor.value == 1) return "";
  return `1 ${props.purchaseUnit.symbol} = ${getConversionFactor.value} x ${model.value?.name} (${model.value?.symbol})`;
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});
</script>
