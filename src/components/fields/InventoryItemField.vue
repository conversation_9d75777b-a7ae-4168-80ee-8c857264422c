<template>
  <auto-complete
    v-model="model"
    :items="filteredInventoryItems"
    v-bind="$attrs"
    :label="label"
    :multiple="multiple"
    item-title="name"
    item-value="id"
    :filter-keys="['title', 'raw.code', 'raw.hsnCode']"
    :loading="loading"
    :disabled="showOnly"
  >
    <template v-slot:item="{ item, props }">
      <v-list-item class="mb-1" v-bind="props" :subtitle="item.raw.subtitle">
        <template #prepend v-if="multiple">
          <v-checkbox
            :model-value="model.includes(item.value)"
            density="compact"
            hide-details
            color="primary"
            class="mr-2"
          ></v-checkbox>
        </template>
        <!-- Default Chip -->
        <template #append>
          <v-icon v-if="!item.raw.stockable" icon="mdi-fruit-cherries" color="error"/>
        </template>
      </v-list-item>
    </template>
  </auto-complete>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Inventory Items"
  },
  multiple: {
    type: Boolean,
    default: false
  },
  /** Component-specific attributes */
  categories: {
    // selected locations (ids)
    type: Array,
    default: () => []
  },
  subCategories: {
    // selected locations (ids)
    type: Array,
    default: () => []
  },
  vendors: {
    // array of vendor ids
    type: Array,
    default: () => []
  },
  exclude: {
    type: Array,
    default: () => []
  },
  showOnly: {
    type: Boolean,
    default: false
  },
  itemFilterOnly: {           
    type: Boolean,
    default: false
  },
  madeItemOnly: {           
    type: Boolean,
    default: false
  },
  boughtItemOnly: {           
    type: Boolean,
    default: false
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const inventoryItems = computed(() => {
  return masterStore.getInventoryItems({
    categoryId: props.categories,
    subCategoryId: props.subCategories,
    vendors: props.vendors
  });
});

const filteredInventoryItems = computed(() => {

  let list = inventoryItems.value;

  if (props.madeItemOnly)
    list = list.filter(i => i.itemType === "made");

  if (props.boughtItemOnly)
    list = list.filter(i => i.itemType === "bought");

  if (!props.exclude.length) return list;

  return list.filter(item => {
    const excludeItem = props.exclude.find(e => e.itemId === item.id);

    if (!excludeItem) return true;

    if (props.itemFilterOnly) return false;

    // Build list of all possible package IDs for this item
    let itemPkgIds = [...item.packages.map(p => p.id)];
    if (!item.packages.length) {
      // if no packages, only the default package exists
      itemPkgIds = ["default"];
    }

    // Check if ALL these pkg IDs exist in props.exclude
    const allPkgsUsed = itemPkgIds.every(pkgId =>
      excludeItem.pkgs.includes(pkgId)
    );

    // If all packages are used → exclude item
    return !allPkgsUsed;
  });
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureInventoryItemData();
  loading.value = false;
});
</script>
