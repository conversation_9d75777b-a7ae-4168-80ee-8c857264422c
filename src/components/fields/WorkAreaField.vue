<template>
  <auto-complete
    ref="waRef"
    v-model="model"
    :items="inventoryLocations"
    v-bind="$attrs"
    :label="label"
    :multiple="multiple"
    item-title="name"
    item-value="id"
    :filter-keys="['title', 'raw.locationName']"
    :loading="loading"
  >
    <template v-slot:item="{ item, props }">
      <v-list-item v-bind="props">
        <template #prepend v-if="multiple">
          <v-checkbox
            :model-value="Array.isArray(model) && model.some(v => (v?.id || v) === item.value)"
            density="compact"
            hide-details
            color="primary"
            class="mr-2"
          ></v-checkbox>
        </template>
        <!-- Default Chip -->
        <template #append v-if="item.raw.isDefault">
          <v-chip class="ml-2" label size="small" density="compact" color="primary">DEFAULT</v-chip>
        </template>
      </v-list-item>
    </template>
  </auto-complete>
</template>

<script setup>
import { onMounted, ref, computed } from "vue";
import { groupListForVuetify } from "@/helpers/vuetifyGroupHelpers.js";
import { useMasterStore } from "@/stores/masterStore";

import AutoComplete from "./AutoComplete.vue";

// ----------------- Props -----------------
const props = defineProps({
  label: {
    type: String,
    default: "Work/Storage Areas"
  },
  multiple: {
    type: Boolean,
    default: false
  },
  /** Component-specific attributes */
  noAuth: {
    // avoid restrict user specific inventoryLocations
    type: Boolean,
    default: false
  },
  locations: {
    // selected locations (ids)
    type: Array,
    default: () => null
  },
  only: {
    // global location selected
    type: [Array, String],
    default: () => null
  },
  exclude: {
    type: [Array, String],
    default: () => null
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

// ----------------- Store & state -----------------
const masterStore = useMasterStore();
const loading = ref(true);

const inventoryLocations = computed(() => {
  const filters = {
    locationId: props.locations,
    id: {
      $in: props.only,
      $not: props.exclude
    }
  };
  const data = masterStore.getInventoryLocations(filters, props.noAuth);
  return groupListForVuetify(data, "locationName");
});

onMounted(async () => {
  loading.value = true;
  await masterStore.ensureData();
  loading.value = false;
});

const waRef = ref(null);
const setDefault = (id) => {
  waRef.value.setDefault(id);
};

defineExpose({
  setDefault
});
</script>
