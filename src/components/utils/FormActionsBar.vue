<template>
  <div>
    <!-- Main Toolbar (App Bar style, elevated on scroll) -->
    <v-app-bar scroll-behavior="elevate">
      <v-container fluid>
        <v-row class="d-flex align-center" dense>
          <!-- Spacer pushes actions to the right -->
          <v-col cols="12" sm="auto" class="d-none d-sm-flex">
            <slot name="custom">
              <span class="text-error text-caption"
                >* Indicates a required field.</span
              >
            </slot>
          </v-col>
          <v-spacer />
          <!-- ⚡ Right Side Action Buttons -->
          <v-col cols="12" sm="auto" class="text-center text-sm-end">
            <!-- Slot: prepend-actions (extra custom actions placed before defaults) -->
            <slot name="prepend-actions"></slot>

            <!-- Default close Button (hidden if hideClose = true) -->
            <v-btn
              v-if="!hideClose"
              variant="outlined"
              color="error"
              class="ml-2"
              :to="backTo"
              @click="$emit('close')"
            >
              <v-icon icon="mdi-close" start></v-icon>
              <span>{{ closeLabel }}</span>
            </v-btn>

            <!-- Slot: actions (fully overridable right side actions) -->
            <slot name="actions">
              <!-- Default Refresh Button (hidden if hideRefresh = true) -->
              <v-btn
                v-if="!hideSubmit"
                :loading="loading"
                :disabled="loading"
                variant="flat"
                color="primary"
                class="ml-2"
                @click="$emit('submit')"
              >
                <span class>{{ submitLabel }}</span>
              </v-btn>
            </slot>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>
    <v-divider />
  </div>
</template>

<script setup>
/**
 * Props
 */
const props = defineProps({
  hideClose: {
    type: Boolean,
    default: false,
  },
  closeLabel: {
    type: String,
    default: "Close",
  },
  hideSubmit: {
    type: Boolean,
    default: false,
  },
  submitLabel: {
    type: String,
    default: "Save",
  },
  loading: {
    type: Boolean,
    default: false,
  },
  backTo: {
    type: Object,
    default: null,
  },
});

/**
 * Emits
 * - close:      fires when close button is clicked
 * - submit:  fires when submit button is clicked
 */
const emit = defineEmits(["close", "submit"]);
</script>
