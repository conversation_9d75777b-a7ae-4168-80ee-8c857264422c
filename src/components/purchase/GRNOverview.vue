<template>
  <v-card border rounded="lg" v-if="show">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- GRN Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title
          >#{{ data.grnNumber }}
          <template #actions>
            <v-btn
              :color="getStatusColor(data.status)"
              variant="tonal"
              :text="data.status"
              size="small"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="grnDetails" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Vendor Details -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title
          >Vendor (#{{ data.vendor.vendorId }})</v-expansion-panel-title
        >
        <v-expansion-panel-text>
          <LabelValueView :details="vendorDetails" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- PO Terms -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title>PO Terms</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView
            v-model="data.vendor.poTerms"
            title="PO Terms"
            :edit="editMode"
          />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Payment Terms -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title>Payment Terms</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView
            v-model="data.vendor.paymentTerms"
            title="Payment Terms"
            :edit="editMode"
          />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Remarks -->
      <v-expansion-panel>
        <v-expansion-panel-title>Remarks</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView
            v-model="data.remarks"
            title="Remarks"
            :edit="editMode"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>

      <!-- Attachments -->
      <v-expansion-panel>
        <v-divider />
        <v-expansion-panel-title>
          Attachments
          <template #actions>
            <attachment-upload-dialog
              type="grn"
              :item-id="data.id"
              :update-db-url="`purchases/grn/${data.id}/attachments`"
              @uploaded="$emit('update')"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <Attachments
            type="grn"
            :item-id="data.id"
            :attachments="data.attachments"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
import { getStatusColor } from "@/helpers/status";

import LabelValueView from "@/components/utils/LabelValueView.vue";
import EditableTextView from "@/components/utils/EditableTextView.vue";
import AttachmentUploadDialog from "@/components/attach/AttachmentUploadDialog.vue";
import Attachments from "@/components/attach/Attachments.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  editMode: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update"]);

const grnDetails = [
  { label: "Location", value: props.data.location?.name || "-" },
  { label: "Workarea", value: props.data.inventoryLocation?.name || "-" },
  { label: "GRN Date", value: props.data.grnDate || "-" },
  { label: "Invoice Number", value: props.data.invoiceNumber || "-" },
  { label: "Invoice Date", value: props.data.invoiceDate || "-" },
  { label: "Created At", value: props.data.createdAt || "-" },
  { label: "Created By", value: props.data.createdBy?.name || "-" },
];

if (props.data.removedBy) {
  const status = props.data.status;
  grnDetails.push(
    {
      label: status == "deleted" ? "Deleted At" : "Returned At",
      value: props.data.removedAt || "-",
    },
    {
      label: status == "deleted" ? "Deleted By" : "Returned By",
      value: props.data.removedBy?.name || "-",
    },
    {
      label: status == "deleted" ? "Deleted Reason" : "Returned Reason",
      value: props.data.reason || "-",
    }
  );
}

const vendor = props.data.vendor || {};
const vendorDetails = [
  { label: "Vendor Name", value: vendor.name || "-" },
  { label: "Contact Name", value: vendor.contactName || "-" },
  { label: "Contact", value: vendor.contactNo || "-" },
  { label: "Email", value: vendor.contactEmailId || "-" },
];

// Conditionally add GSTIN, PAN, CIN, TIN if present
if (vendor.gstNo) vendorDetails.push({ label: "GSTIN", value: vendor.gstNo });
if (vendor.panNo) vendorDetails.push({ label: "PAN", value: vendor.panNo });
if (vendor.cinNo) vendorDetails.push({ label: "CIN", value: vendor.cinNo });
if (vendor.tinNo) vendorDetails.push({ label: "TIN", value: vendor.tinNo });

const show = computed(() => props.data.id || props.data.vendor);
</script>

<style scoped></style>
