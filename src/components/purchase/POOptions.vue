<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      ></v-btn>
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          @click="handleAction(action.id)"
          :prepend-icon="action.icon"
          :title="action.label"
        >
        </v-list-item>

        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>
  <ReasonDialog v-model="rejectDialog" @reject="reject"></ReasonDialog>
  <ReasonDialog
    v-model="closeDialog"
    title="Reason for Close?"
    @reject="close"
  ></ReasonDialog>
</template>
<script setup>
import { computed, inject, ref } from "vue";
import { purchaseStatus } from "@/constants/status";
import { PRIV_CODES } from "@/constants/privilegeCodes";
import { checkUserPrivilege } from "@/router/middleware";
import { useRouter } from "vue-router";
import { useSnackbarStore } from "@/stores/snackBar";

import ReasonDialog from "@/components/base/ReasonDialog.vue";
import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import httpClient from "@/plugin/Axios";

const purchaseOrderStore = usePurchaseOrderStore();
const { showSnackbar } = useSnackbarStore();

const props = defineProps({
  icon: {
    type: Boolean,
    default: false,
  }, // "icon" or "text"
  status: {
    type: String,
    required: true,
  },
  itemId: {
    type: [String, Number],
    required: true,
  },
  poNumber: {
    type: String,
    required: false,
  },
});

const emit = defineEmits(["refresh"]);
const router = useRouter();

// Map status → allowed action IDs
const statusActionMap = {
  [purchaseStatus.DRAFT]: ["edit", "close"],
  [purchaseStatus.SUBMITTED]: [
    "approve",
    "reject",
    "close",
    "printPDF",
    "printXLS",
    "sendMail",
  ],
  [purchaseStatus.APPROVED]: [
    "createGrn",
    "close",
    "printPDF",
    "printXLS",
    "sendMail",
  ],
  [purchaseStatus.PARTIAL]: ["createGrn", "printPDF", "printXLS", "sendMail"],
  [purchaseStatus.COMPLETED]: ["printPDF", "printXLS", "sendMail"],
  [purchaseStatus.REJECTED]: [],
  [purchaseStatus.DELETED]: [],
};

// Actions with privilege codes
const actions = [
  {
    id: "edit",
    label: "Edit",
    icon: "mdi-pencil",
    privilege_code: PRIV_CODES.PUR_PO,
  },
  {
    id: "approve",
    label: "Approve",
    icon: "mdi-check-decagram",
    privilege_code: PRIV_CODES.PUR_APPROVE_PO,
  },
  {
    id: "reject",
    label: "Reject",
    icon: "mdi-close-octagon-outline",
    privilege_code: PRIV_CODES.PUR_APPROVE_PO,
  },
  {
    id: "createGrn",
    label: "Create GRN",
    icon: "mdi-transfer-right",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
  {
    id: "printPDF",
    label: "Export as PDF",
    icon: "mdi-file-pdf-box",
    privilege_code: PRIV_CODES.PUR_PO,
  },
  // {
  //   id: "printXLS",
  //   label: "Export as XLS",
  //   icon: "mdi-file-excel-box",
  //   privilege_code: PRIV_CODES.PUR_PO,
  // },
  {
    id: "sendMail",
    label: "Send Mail",
    icon: "mdi-email-arrow-right",
    privilege_code: PRIV_CODES.PUR_PO,
  },
  {
    id: "close",
    label: "Close",
    icon: "mdi-close-circle-outline",
    privilege_code: PRIV_CODES.PUR_APPROVE_PO,
  },
];

// Filter actions based on status & privilege
const availableOptions = computed(() => {
  const allowed = statusActionMap[props.status] || [];
  return actions.filter(
    (action) =>
      allowed.includes(action.id) && checkUserPrivilege(action.privilege_code)
  );
});

const handleAction = (actionId) => {
  switch (actionId) {
    case "edit":
      router.push({
        name: "edit-purchase-order",
        params: { id: props.itemId },
      });
      break;
    case "approve":
      handleApprove();
      break;
    case "reject":
      rejectDialog.value = true;
      break;
    case "close":
      closeDialog.value = true;
      break;
    case "createGrn":
      router.push({
        name: "create-grn",
        params: { id: props.itemId },
      });
      break;
    case "printPDF":
      exportPDF();
      break;
    case "printXLS":
      exportPDF();
      break;
    case "sendMail":
      sendEmail();
      break;
    default:
      break;
  }
};

const $confirm = inject("confirm");

const handleApprove = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  const confirmed = await $confirm(
    "Are you sure you want to approve this purchase order?",
    { title: "Approve Purchase Order" }
  );
  if (!confirmed) return;
  await purchaseOrderStore.approvePurchaseOrder(props.itemId);
  emit("refresh");
};

const rejectDialog = ref(false);
const reject = async (reason) => {
  try {
    await purchaseOrderStore.rejectPurchaseOrder(props.itemId, reason);
    rejectDialog.value = false;
    emit("refresh");
  } catch (err) {
    console.error(err);
  }
};

const closeDialog = ref(false);

const close = async (reason) => {
  try {
    await purchaseOrderStore.closePurchaseOrder(props.itemId, reason);
    closeDialog.value = false;
    emit("refresh");
  } catch (err) {
    console.error(err);
  }
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`purchase-orders/${props.itemId}/pdf`, {
      responseType: "blob",
    });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

const sendEmail = async () => {
  $loader.show("Please wait...");
  try {
    const { data } = await httpClient.get(
      `purchase-orders/${props.itemId}/email`
    );
    showSnackbar("success", data.message);
  } catch ({ response }) {
    console.error(response.data.message);
    showSnackbar("error", response.data.message);
  } finally {
    $loader.hide();
  }
};

const disabled = computed(() => availableOptions.value.length === 0);
</script>
