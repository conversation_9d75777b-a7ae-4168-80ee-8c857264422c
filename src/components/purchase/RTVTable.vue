<template>
  <v-container fluid class="px-0 pb-0">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="grnList"
          :headers="grnListHeaders"
          items-per-page="-1"
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>

          <template #item.itemName="{ item }">
            <div class="d-flex justify-space-between align-center">
              <formatted-item-name :item="item" show-foc></formatted-item-name>
            </div>
          </template>

          <template #item.receivedQty="{ item }">
            {{ item.receivedQty || "-" }}
          </template>

          <template #item.rtvQty="{ item, index }">
            <v-text-field
              v-model.number="item.rtvQty"
              type="number"
              min="1"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'rtvQty')"
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.uom="{ item }">
            {{ item.pkgUOM || item.purchaseUOM }}
          </template>

          <template #item.taxRate="{ item, index }">
            <span> {{ item.taxes[0]?.name || "-" }} </span>
          </template>

          <template #item.netAmount="{ item }">
            {{ truncateNumber(item.netAmount) }}
          </template>

          <template #item.totalTaxAmount="{ item }">{{
            truncateNumber(item.totalTaxAmount)
          }}</template>

          <template #item.totalAmount="{ item }">{{
            truncateNumber(item.totalAmount)
          }}</template>

          <template #item.remarks="{ item }">
            <v-textarea
              v-model="item.remarks"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              rows="1"
              auto-grow
            ></v-textarea>
          </template>

          <template #bottom>
            <cart-summary
              :data="{
                ...cart,
                total: cart.totalAmount,
                subtotal: cart.netAmount,
                discount: cart.totalDiscount,
                focAmount: cart.totalFocAmount,
                transportCharge: 0,
                otherCharges: 0,
                totalTaxAmount: cart.totalTaxAmount,
              }"
            />
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import { grnRTVHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import CartSummary from "@/components/purchase/CartSummary.vue";

const props = defineProps({
  grnList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits(["edit", "calculate"]);

const grnListHeaders = grnRTVHeaders;

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }
};
</script>
