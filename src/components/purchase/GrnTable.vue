<template>
  <v-container fluid class="px-0 pb-0">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="grnList"
          :headers="grnListHeaders"
          items-per-page="-1"
          hide-default-footer
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon
              color="error"
              @click="$emit('removeItem', index)"
              @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template>
          <template #item.itemName="{ item, index }">
            <div class="d-flex justify-space-between align-center">
              <formatted-item-name
                :item="item"
                show-foc
                editable
                @update-foc="$emit('edit', item, index)"
              ></formatted-item-name>

              <v-icon
                size="small"
                color="primary"
                class="cursor-pointer"
                @click="duplicateItem(item)"
                >mdi-content-copy</v-icon
              >
            </div>
          </template>

          <template #item.orderedQty="{ item }">
            {{ item.orderedQty || "-" }}
          </template>

          <template #item.receivedQty="{ item, index }">
            <v-text-field
              v-model.number="item.receivedQty"
              type="number"
              min="1"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'receivedQty')"
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.uom="{ item }">
            {{ item.pkgUOM || item.purchaseUOM }}
          </template>

          <template #item.unitCost="{ item }">
            <v-text-field
              v-model.number="item.unitCost"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'unitCost')"
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.totalDiscount="{ item, index }">
            <v-text-field
              v-model.number="item.totalDiscount"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalDiscount')"
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.taxRate="{ item, index }">
            <tax-field
              v-model="item.taxes"
              multiple
              return-object
              @update:model-value="$emit('edit', item, index)"
            ></tax-field>
            <!-- :hint="`Total: ${String(selectedTaxAmount(item))}%`"
              persistent-hint -->
          </template>

          <template #item.totalCess="{ item, index }">
            <v-text-field
              v-model.number="item.totalCess"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalCess')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.netAmount="{ item }">
            {{ truncateNumber(item.netAmount) }}
          </template>

          <template #item.totalTaxAmount="{ item }">{{
            truncateNumber(item.totalTaxAmount)
          }}</template>

          <template #item.totalAmount="{ item }">{{
            truncateNumber(item.totalAmount)
          }}</template>

          <template #bottom>
            <cart-summary
              editable
              :data="{
                ...cart,
                total: cart.totalAmount,
                subtotal: cart.netAmount,
                discount: cart.totalDiscount,
                focAmount: cart.totalFocAmount,
                transportCharge: 0,
                otherCharges: 0,
                totalTaxAmount: cart.totalTaxAmount,
              }"
              @update-charges="(charge, tax) => $emit('calculate', charge, tax)"
            />
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import { getPurchaseItemHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import CartSummary from "@/components/purchase/CartSummary.vue";
import TaxField from "@/components/fields/TaxField.vue";

const props = defineProps({
  grnList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits(["removeItem", "edit", "copy", "calculate"]);

const grnListHeaders = getPurchaseItemHeaders({
  mode: "grn",
  type: "edit",
});

const selectedTaxAmount = (item) => {
  return item.taxes.reduce((acc, tax) => {
    return acc + tax.value;
  }, 0);
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "receivedQty") {
    if (item.receivedQty < 1 || !item.receivedQty) {
      item.receivedQty = 1;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: discount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost * item.quantity) {
    item.totalDiscount = item.unitCost * item.quantity;
  }
};

const duplicateItem = (item) => {
  emit("copy", item);
};
</script>
