<template>
  <v-container fluid class="px-0 mb-10">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered po-table-freeze-cols po-table-freeze-cols--col-6"
        :items="poList"
        :headers="headers"
        items-per-page="-1"
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>
        <template #item.actions="{ index }">
          <v-icon
            color="error"
            @click="$emit('removeItem', index)"
            @keydown.enter="$emit('removeItem', index)"
            >mdi-close</v-icon
          >
        </template>

        <template #item.itemName="{ item, index }">
          <div class="d-flex justify-space-between align-center">
            <formatted-item-name
              :item="item"
              show-foc
              editable
              @update-foc="$emit('edit', item, index)"
            ></formatted-item-name>

            <v-icon
              size="small"
              color="primary"
              class="cursor-pointer"
              @click="duplicateItem(item)"
              >mdi-content-copy</v-icon
            >
          </div>
        </template>
        <template #item.orderedQty="{ item }">
          {{ item.orderedQty || item.quantity }}
        </template>
        <template #item.pendingQty="{ item }">
          {{ getPendingQty(item) }}
        </template>

        <template #item.receivedQty="{ item, index }">
          <!-- v-if="props.selectedWorkArea.length <= 1" -->
          <v-text-field
            v-model.number="item.receivedQty"
            type="number"
            min="1"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            :readonly="props.selectedWorkArea.length > 1"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'receivedQty')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
        </template>

        <template #item.purchaseUOM="{ item }">
          {{ item.pkgUOM || item.purchaseUOM }}
        </template>

        <template #item.unitCost="{ item, index }">
          <v-text-field
            v-model.number="item.unitCost"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'unitCost')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
        </template>

        <template #item.totalDiscount="{ item, index }">
          <v-text-field
            v-model.number="item.totalDiscount"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'totalDiscount')"
            @update:model-value="$emit('edit', item, index)"
          >
          </v-text-field>
        </template>

        <template #item.totalCess="{ item, index }">
          <v-text-field
            v-model.number="item.totalCess"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item, 'totalCess')"
            @update:model-value="$emit('edit', item, index)"
          ></v-text-field>
        </template>

        <template #item.taxRate="{ item, index }">
          <tax-field
            v-model="item.taxes"
            multiple
            return-object
            @update:model-value="$emit('edit', item, index)"
          ></tax-field>
          <!-- :hint="`Total: ${String(selectedTaxAmount(item))}%`"
            persistent-hint -->
        </template>
        <template #item.netAmount="{ item }">
          {{ truncateNumber(item.netAmount) }}
        </template>

        <template #item.totalTaxAmount="{ item }">
          {{ truncateNumber(item.totalTaxAmount) }}
        </template>

        <template #item.totalAmount="{ item }">{{
          truncateNumber(item.totalAmount)
        }}</template>

        <!-- Dynamic Work Area Fields -->
        <template
          v-for="wa in selectedWorkArea"
          v-slot:[`item.${wa.id}`]="{ item, index }"
        >
          <!-- Ensure workAreaQty object exists -->
          <template v-if="ensureWorkAreaObject(item)"></template>

          <v-text-field
            v-model.number="item.workAreaQty[wa.id]"
            type="number"
            variant="outlined"
            density="compact"
            color="primary"
            hide-details="auto"
            @keydown.up.prevent
            @keydown.down.prevent
            @blur="() => blurField(item.workAreaQty, wa.id)"
            @update:model-value="handleWorkareaQuantity(item, index)"
          />
          <!-- @update:model-value="$emit('edit', item, index)" -->
        </template>

        <template #bottom>
          <cart-summary
            editable
            :data="{
              ...cart,
              total: cart.totalAmount,
              subtotal: cart.netAmount,
              discount: cart.totalDiscount,
              focAmount: cart.totalFocAmount,
              transportCharge: 0,
              otherCharges: 0,
              totalTaxAmount: cart.totalTaxAmount,
            }"
            @update-charges="(charge, tax) => $emit('calculate', charge, tax)"
          />
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import CartSummary from "./CartSummary.vue";
import TaxField from "@/components/fields/TaxField.vue";

const props = defineProps({
  poList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  selectedWorkArea: {
    type: Array,
    default: () => [],
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits([
  "edit",
  "updateQty",
  "calculate",
  "copy",
  "removeItem",
]);

// const selectedTaxAmount = (item) => {
//   return item.taxes.reduce((acc, tax) => {
//     return acc + tax.value;
//   }, 0);
// };

// const updateReceivedQty = (v, item, index) => {
//   emit("edit", item, index);
//   emit("updateQty", v);
// };

const ensureWorkAreaObject = (item) => {
  // First time initialization
  if (!item.workAreaQty) {
    item.workAreaQty = {};
  }

  // Ensure EVERY selected work area has a value
  props.selectedWorkArea.forEach((wa, ind) => {
    if (item.workAreaQty[wa.id] == null) {
      // First work area (default): assign receivedQty ONLY if creating fresh
      if (ind === 0 && Object.keys(item.workAreaQty).length === 0) {
        item.workAreaQty[wa.id] = item.receivedQty;
      } else {
        // others start at 0
        item.workAreaQty[wa.id] = 0;
      }
    }
  });

  return true;
};

const getPendingQty = (item) => {
  const ordered = Number(item.quantity) || 0;
  const received = Number(item.receivedQty) || 0;

  const pending = ordered - (item.totalReceivedQty + received);

  return pending > 0 ? pending : 0;
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: discount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost * item.quantity) {
    item.totalDiscount = item.unitCost * item.quantity;
  }
};

const duplicateItem = (item) => {
  emit("copy", item);
};

const handleWorkareaQuantity = (item, index) => {
  if (!item.workAreaQty) return;

  // 1. SUM all work area quantities
  const total = Object.values(item.workAreaQty)
    .map((qty) => Number(qty) || 0)
    .reduce((a, b) => a + b, 0);

  // 2. Update receivedQty automatically
  item.receivedQty = total;

  // 3. Build grouped array for DirectIssue
  const grouped = Object.entries(item.workAreaQty)
    .filter(([_, qty]) => qty > 0)
    .map(([workAreaId, qty]) => ({
      workAreaId,
      qty,
    }));

  // 4. Emit to parent
  emit("edit", item, index);
  emit("updateQty", { item, grouped });
};
</script>
