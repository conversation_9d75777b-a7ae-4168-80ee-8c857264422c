<template>
  <div>
    <v-fab
      v-if="!openNav && showAdd"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel
          v-model="filter"
          :selected-vendor-id="selectedVendorId"
        />

        <v-card-text>
          <v-form ref="form" v-smart-tab="smartTab">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    persistent-hint
                    :hint="`HSN Code: ${cartItem.hsnCode || '-'}`"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :vendors="filter.vendors"
                    :exclude="excludedItems"
                    :showOnly="!!currentSelectedItem?.itemId"
                    autofocus
                  />
                </v-col>
                <v-col cols="12" v-if="!selectedVendorId && selectedItem">
                  <vendor-field
                    :only="selectedItem.vendors"
                    v-model="cartItem.vendor"
                    return-object
                    @update:model-value="onChangeVendor"
                  />
                </v-col>

                <v-col cols="12">
                  <v-autocomplete
                    class="required-fld"
                    v-model="cartItem.pkg"
                    :items="filteredPackages"
                    item-title="name"
                    item-value="id"
                    label="Package/UOM"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    :readonly="filteredPackages.length == 1"
                    return-object
                    :menu-icon="
                      filteredPackages.length == 1 ? '' : 'mdi-menu-down'
                    "
                    :clear-icon="
                      filteredPackages.length == 1 ? '' : 'mdi-close-circle'
                    "
                    @update:model-value="
                      (v) => handleInventoryPkg(v, cartItem.vendor?.id)
                    "
                  >
                  </v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.quantity"
                    label="Quantity"
                    type="number"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${cartItem.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'quantity')"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.unitCost"
                    label="Unit Cost"
                    type="number"
                    v-highlight-on-focus
                    :rules="[rules.price, rules.positive]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    :readonly="!!cartItem.contractId"
                    persistent-hint
                    :hint="cartItem.contractNumber"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'unitCost')"
                  >
                    <!-- indicate contract -->
                    <template #prepend-inner v-if="cartItem.contractId">
                      <v-icon
                        size="small"
                        icon="mdi-file-sign"
                        color="purple"
                      />
                    </template>

                    <template #append-inner>
                      <purchase-history
                        :item="cartItem"
                        :inventoryLocationId="inventoryLocationId"
                      />
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.totalDiscount"
                    label="Discount"
                    type="number"
                    :rules="[
                      rules.price,
                      rules.positive,
                      (v) =>
                        rules.maxValue(
                          v,
                          cartItem.unitCost * cartItem.quantity
                        ),
                    ]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'totalDiscount')"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.totalCess"
                    :rules="[rules.price, rules.positive]"
                    label="Cess"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'totalCess')"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <tax-field
                    v-model="cartItem.taxes"
                    multiple
                    return-object
                    :hint="selectedTaxAmount"
                    persistent-hint
                  ></tax-field>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="cartItem.remarks"
                    label="Remarks"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details
                    rows="2"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="onSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch } from "vue";
import rules from "@/helpers/rules";
import cartHelper from "@/helpers/cartHelper";

import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useMasterStore } from "@/stores/masterStore";

// components
import InventoryFiltersPanel from "./InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import TaxField from "@/components/fields/TaxField.vue";
import PurchaseHistory from "./PurchaseHistory.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: "",
  },
  inventoryLocationId: {
    type: String,
    default: "",
  },
  selectedVendorId: {
    type: String,
    default: null,
  },
  existingItems: { type: Array, default: () => [] },
  currentSelectedItem: { type: Object, default: () => null },
  showAdd: {
    type: Boolean,
    default: true,
  },
});

const inventoryStore = useInventoryItemStore();
const masterStore = useMasterStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
  vendors: [],
});

const cartItem = ref(cartHelper.NewCartItem());
const packageList = ref([]);
const selectedItem = ref([]);

const taxes = computed(() => {
  return masterStore.getTaxes();
});

const inventoryItems = computed(() => {
  return masterStore.getInventoryItems();
});

const selectedTaxAmount = computed(() => {
  let totalTax = 0;
  const netAmount = cartItem.value.unitCost * cartItem.value.quantity;

  cartItem.value.taxes.forEach((tax) => {
    let val = 0;

    if (tax.valueType == "amount") {
      // fixed amount tax
      val = Number(tax.valueAmt) || 0;
    } else if (tax.valueType == "percentage") {
      // percentage tax
      val = ((Number(tax.valuePercentage) || 0) * netAmount) / 100;
    }

    tax.valueAmt = val; // store final calculated tax value
    totalTax += val;
  });
  return `Total: ${String(totalTax)}`;
});

const filteredPackages = computed(() => {
  if (props.currentSelectedItem) {
    return packageList.value;
  }
  const item = selectedItem.value;
  if (!item || !item.id) return [];

  const chosenPkgIds = props.existingItems
    .filter((i) => i.itemId === item.id)
    .map((i) => i.pkg?.id);

  return packageList.value.filter((pkg) => !chosenPkgIds.includes(pkg.id));
});

const excludedItems = computed(() => {
  const map = {};

  props.existingItems.forEach((i) => {
    if (!i.itemId || !i.pkg?.id) return;

    if (!map[i.itemId]) {
      map[i.itemId] = new Set();
    }

    map[i.itemId].add(i.pkg.id);
  });

  return Object.entries(map).map(([itemId, pkgs]) => ({
    itemId,
    pkgs: Array.from(pkgs),
  }));
});

watch(filteredPackages, (list) => {
  if (list.length > 0) {
    cartItem.value.pkg = list[0];
    const vendor = cartItem.value.vendor?.id || props.selectedVendorId;
    handleInventoryPkg(cartItem.value.pkg, vendor);
  } else {
    cartItem.value.pkg = null;
  }
});

const onSelectInventoryItem = async (selected) => {
  selectedItem.value = selected;

  // build package list
  packageList.value = [...selected.packages];

  if (selected.defaultPackage) {
    packageList.value.unshift({
      name: `${selected.purchaseUnit?.symbol}`,
      id: "default",
    });
  }

  // pkg will be auto-updated by the filteredPackages watcher
};

const onChangeVendor = (selected) => {
  if (!selected) return;
  handleInventoryPkg(cartItem.value.pkg, selected.id);
};

const resolveUnitCost = (item, v) => {
  if (item.contractPrice) return item.contractPrice;
  if (item.lastGrnPrice) return item.lastGrnPrice;

  if (v && v.id !== "default") {
    const pkg = item.packages.find((p) => p.id === v.id);
    if (pkg) return pkg.unitCost;
  }

  return item.unitCost;
};

const handleInventoryPkg = async (
  v,
  selectedVendor = props.selectedVendorId
) => {
  if (!v || !selectedVendor) return;
  const item = await inventoryStore.fetchItemDetails(
    {
      id: selectedItem.value.id,
      locationId: props.locationId,
      inventoryLocationId: props.inventoryLocationId,
      vendorId: selectedVendor,
      pkgId: v.id,
    },
    true
  );

  // select default vendor
  cartItem.value.vendor =
    item.vendors.find((v) => v.id === selectedVendor) || item.vendors[0];

  cartItem.value.itemName = item.itemName;
  cartItem.value.itemCode = item.itemCode;
  cartItem.value.itemId = item.id;
  cartItem.value.categoryId = item.category?.id;
  cartItem.value.categoryName = item.category?.name;
  cartItem.value.subcategoryId = item.subCategory?.id;
  cartItem.value.subcategoryName = item.subCategory?.name;
  cartItem.value.hsnCode = item.hsnCode || "";
  cartItem.value.purchaseUOM = item.purchaseUnit.symbol;
  cartItem.value.contractPrice = item.contractPrice || 0;
  cartItem.value.contractType = item.contractType || null;
  cartItem.value.contractId = item.contractId || null;
  cartItem.value.contractNumber = item.contractNumber || null;
  cartItem.value.inStock = item.inStock;
  cartItem.value.inclTax = item.inclTax || false;
  cartItem.value.stockable = item.stockable;

  cartItem.value.pkgUOM =
    v.id === "default" ? item.purchaseUnit.symbol : v.name;

  cartItem.value.unitCost = resolveUnitCost(item, v);

  // fetch tax object
  const ids = item.taxes.map((item) => item.id);
  cartItem.value.taxes =
    taxes.value.filter((tax) => ids.includes(tax.id)) || [];
};

const onSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...cartItem.value };

  if (payload.pkg && payload.pkg.id === "default") {
    payload.pkg.quantity = payload.quantity;
  }
  // if (!payload.pkg) {
  //   payload.pkg = {
  //     id: "default",
  //     name: "Default",
  //   };
  // }
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  cartItem.value = cartHelper.NewCartItem();
  selectedItem.value = null;
  form.value.resetValidation();

  // ✅ Focus back to the first field
  smartTab.focusFirst();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: totalDiscount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost * item.quantity) {
    item.totalDiscount = item.unitCost * item.quantity;
  }
};

const smartTab = reactive({
  onSubmit,
});

watch(
  openNav,
  (val) => {
    if (val) smartTab.focusFirst();

    if (!props.currentSelectedItem) return;

    // GRN Edit
    cartItem.value = {
      ...props.currentSelectedItem,
      quantity: 0,
    };
    selectedItem.value = inventoryItems.value.find(
      (item) => item.id === props.currentSelectedItem.itemId
    );
    onSelectInventoryItem(selectedItem.value);
  },
  {
    immediate: true,
  }
);
</script>
