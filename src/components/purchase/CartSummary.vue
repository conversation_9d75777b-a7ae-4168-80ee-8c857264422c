<template>
  <v-card flat class="w-100">
    <v-divider></v-divider>

    <v-card-text>
      <!-- Autocomplete -->
      <v-row dense class="mb-2" v-if="editable">
        <v-spacer></v-spacer>
        <v-col cols="6" sm="3">
          <v-autocomplete
            v-model="selectedItems"
            :items="otherCharges"
            item-title="name"
            item-value="id"
            label="Show Other Tax / Charge"
            hide-details
            inset
            density="compact"
            color="primary"
            variant="outlined"
            clearable
            multiple
            return-object
          />
        </v-col>
      </v-row>

      <!-- Summary -->
      <v-row no-gutters v-for="(item, i) in summaryItems" :key="i">
        <v-col class="text-end">{{ item.label }}:</v-col>
        <v-col
          cols="auto"
          class="pl-3 text-end text-subtitle-1"
          style="width: 250px"
        >
          {{ item.value }}
        </v-col>
      </v-row>

      <!-- Taxes -->
      <div v-if="editable && taxes.length" class="mt-2">
        <v-row dense v-for="tax in taxes" :key="tax.id">
          <v-col class="text-end">{{ tax.name }}:</v-col>
          <v-col cols="auto" class="pl-3" style="width: 250px">
            <v-text-field
              v-if="tax.valueType == 'amount'"
              v-model.number="tax.valueAmt"
              density="compact"
              variant="outlined"
              type="number"
              hide-details
              color="primary"
              @blur="blurField(tax, 'valueAmt')"
              @update:model-value="emitCharges"
            />
            <v-text-field
              v-else
              v-model.number="tax.valuePercentage"
              density="compact"
              variant="outlined"
              type="number"
              color="primary"
              suffix="%"
              @blur="blurField(tax, 'valuePercentage')"
              @update:model-value="emitCharges"
            />
            <!-- :hint="`in Amt: ${tax.valueAmt}`"
              persistent-hint -->
          </v-col>
        </v-row>
      </div>

      <!-- Charges -->
      <div v-if="editable && charges.length" class="mt-2">
        <v-row dense v-for="charge in charges" :key="charge.id">
          <v-col class="text-end">{{ charge.name }}:</v-col>
          <v-col cols="auto" class="pl-3" style="width: 250px">
            <v-text-field
              v-if="charge.valueType == 'amount'"
              v-model.number="charge.valueAmt"
              density="compact"
              variant="outlined"
              type="number"
              hide-details
              color="primary"
              @blur="blurField(charge, 'valueAmt')"
              @update:model-value="emitCharges"
            />
            <v-text-field
              v-else
              v-model.number="charge.valuePercentage"
              density="compact"
              variant="outlined"
              type="number"
              color="primary"
              suffix="%"
              @blur="blurField(charge, 'valuePercentage')"
              @update:model-value="emitCharges"
            />
            <!-- :hint="`in Amt: ${charge.valueAmt}`"
              persistent-hint -->
          </v-col>
        </v-row>
      </div>

      <!-- Total -->
      <v-row no-gutters class="py-2">
        <v-col class="text-end text-subtitle-1">Total:</v-col>
        <v-col
          cols="auto"
          class="pl-3 text-end text-subtitle-1 font-weight-bold"
          style="width: 250px"
        >
          {{ data.total.toFixed(2) }}
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { computed, ref } from "vue";
import { useMasterStore } from "@/stores/masterStore";
import { groupListForVuetify } from "@/helpers/vuetifyGroupHelpers.js";

const props = defineProps({
  data: { type: Object, required: true },
  editable: { type: Boolean, default: false },
});

const masterStore = useMasterStore();

// console.log(props.data, "props");
const otherCharges = computed(() => {
  const net = props.data.netAmount;
  const taxes = masterStore
    .getTaxes()
    .filter((t) => t.taxLevel == "grn")
    .map((tax) => {
      const data = { ...tax, type: "tax" };
      if (tax.valueType == "amount") return data;

      return {
        ...data,
        type: "tax",
        valueAmt: ((Number(tax.valuePercentage) || 0) * net) / 100,
      };
    });

  const charges = masterStore.getCharges().map((charge) => {
    if (charge.valueType == "amount") return charge;

    return {
      ...charge,
      valueAmt: ((Number(charge.valuePercentage) || 0) * net) / 100,
    };
  });
  const result = [...taxes, ...charges];
  return groupListForVuetify(result, "type");
});

const emit = defineEmits(["updateCharges"]);

// initial values from parent
const charges = ref([...props.data.charges]);

const taxes = ref([...props.data.taxes]);

// Unified selected items (charges + taxes)
const selectedItems = computed({
  get() {
    return [...taxes.value, ...charges.value];
  },
  set(newList) {
    handleChargesList(newList);
  },
});

// Summary section
const summaryItems = computed(() => {
  let res = [
    { label: "Subtotal", value: props.data.subtotal.toFixed(2) },
    { label: "Discount", value: `- ${props.data.discount.toFixed(2)}` },
    { label: "FOC", value: `- ${props.data.focAmount.toFixed(2)}` },
    {
      label: "Tax Amount",
      value: props.data.totalTaxAmount.toFixed(2),
    },
  ];

  if (!props.editable) {
    props.data.taxes.forEach((tax) => {
      res.push({
        label: tax.name,
        value: tax.valueAmt.toFixed(2),
      });
    });

    props.data.charges.forEach((c) => {
      res.push({
        label: c.name,
        value: c.valueAmt.toFixed(2),
      });
    });
  }

  return res;
});

// Emit changes
const emitCharges = () => {
  emit("updateCharges", charges.value, taxes.value);
};

// Ensure number validity
const blurField = (item, field) => {
  if (!item[field] || item[field] < 0) {
    item[field] = 0;
  }
};

// Handle add/remove from autocomplete
const handleChargesList = (selectedList) => {
  taxes.value = [];
  charges.value = [];

  selectedList.forEach((c) => {
    if (c.type === "tax") {
      taxes.value.push({
        id: c.id,
        name: c.name,
        type: "tax",
        taxLevel: c.taxLevel,
        valueType: c.valueType,
        valueAmt: c.valueAmt,
        valuePercentage: c.valuePercentage,
      });
    } else {
      charges.value.push({
        id: c.id,
        name: c.name,
        type: "charge",
        valueAmt: c.valueAmt,
        valuePercentage: c.valuePercentage,
        valueType: c.valueType,
      });
    }
  });

  emitCharges();
};
</script>

<style scoped></style>
