<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        v-if="icon"
        v-bind="props"
        variant="text"
        icon="mdi-dots-vertical-circle-outline"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      />
      <v-btn
        v-else
        v-bind="props"
        variant="outlined"
        prepend-icon="mdi-dots-vertical"
        text="Options"
        color="primary"
        class="ml-2"
        :disabled="disabled"
      ></v-btn>
    </template>

    <v-list class="pa-0">
      <div v-for="(action, index) in availableOptions" :key="action.id">
        <v-list-item
          @click="handleAction(action.id)"
          :prepend-icon="action.icon"
          :title="action.label"
        >
        </v-list-item>

        <v-divider v-if="index < availableOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>

  <GrnReturn
    v-model="reasonDialog"
    @submit="submit"
    :title="reasonTitle"
  ></GrnReturn>

  <!-- Upload Dialog -->
  <attachment-upload-dialog
    v-model="showUploadDialog"
    type="grn"
    :item-id="itemId"
    :update-db-url="`purchases/grn/${itemId}/attachments`"
    @uploaded="onUploaded"
    add
  />
</template>
<script setup>
import { computed, ref, inject } from "vue";
import { purchaseStatus } from "@/constants/status";
import { PRIV_CODES } from "@/constants/privilegeCodes";
import { checkUserPrivilege } from "@/router/middleware";
import AttachmentUploadDialog from "@/components/attach/AttachmentUploadDialog.vue";
import GrnReturn from "@/views/grn/GrnReturn.vue";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import httpClient from "@/plugin/Axios";
import { useRouter } from "vue-router";

const grnStore = useGrnStore();

const props = defineProps({
  icon: {
    type: Boolean,
    default: false,
  }, // "icon" or "text"
  status: {
    type: String,
    required: true,
  },
  itemId: {
    type: [String, Number],
    required: true,
  },
  filters: { type: Object, default: () => ({}) },
});

const emit = defineEmits(["refresh"]);

// Map status → allowed action IDs
const statusActionMap = {
  [purchaseStatus.COMPLETED]: [
    "edit",
    "returnVendor",
    "delete",
    "upload",
    "printPDF",
    "printXLS",
  ],
  [purchaseStatus.DELETED]: ["printPDF", "printXLS"],
  [purchaseStatus.RETURN_VENDOR]: ["printPDF", "printXLS"],
};

// Actions with privilege codes
const actions = [
  {
    id: "edit",
    label: "Edit",
    icon: "mdi-pencil",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
  {
    id: "returnVendor",
    label: "Return to Vendor",
    icon: "mdi-send-variant",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
  {
    id: "upload",
    label: "New Attachments",
    icon: "mdi-cloud-plus",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
  {
    id: "printPDF",
    label: "Export as PDF",
    icon: "mdi-file-pdf-box",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
  // {
  //   id: "printXLS",
  //   label: "Export as XLS",
  //   icon: "mdi-file-excel-box",
  //   privilege_code: PRIV_CODES.PUR_GRN,
  // },
  {
    id: "delete",
    label: "Delete",
    icon: "mdi-delete",
    privilege_code: PRIV_CODES.PUR_GRN,
  },
];

// Filter actions based on status & privilege
const availableOptions = computed(() => {
  const allowed = statusActionMap[props.status] || [];
  return actions.filter(
    (action) =>
      allowed.includes(action.id) && checkUserPrivilege(action.privilege_code)
  );
});

const reasonDialog = ref(false);
const type = ref(null);
const reasonTitle = ref(null);
const router = useRouter();

const handleAction = (actionId) => {
  switch (actionId) {
    case "edit":
      router.push({
        name: "edit-grn",
        params: { id: props.itemId },
      });
      break;
    case "returnVendor":
      router.push({
        name: "return-grn",
        params: { id: props.itemId },
      });
      break;
    case "delete":
      reasonDialog.value = true;
      type.value = "delete";
      reasonTitle.value = "Delete GRN";
      break;
    case "printPDF":
      exportPDF();
      break;
    case "printXLS":
      exportPDF();
      break;
    case "upload":
      upload();
      break;
    default:
      break;
  }
};

const showUploadDialog = ref(false);

const upload = () => {
  showUploadDialog.value = true;
};

const onUploaded = async () => {
  showUploadDialog.value = false;
  await grnStore.fetchGrnList(props.filters);
};

const submit = async (reason) => {
  try {
    if (type.value === "delete") await grnStore.deleteGRN(props.itemId, reason);
    else await grnStore.returnGRN(props.itemId, reason);
    emit("refresh");
  } catch (error) {
    console.error("Error deleting GRN:", error);
  }
};

const $loader = inject("loader");

const exportPDF = async () => {
  $loader.show("Please wait...");
  try {
    await httpClient.get(`purchases/${props.itemId}/pdf`, {
      responseType: "blob",
    });
  } catch ({ response }) {
    console.error(response.data.message);
  } finally {
    $loader.hide();
  }
};

const disabled = computed(() => availableOptions.value.length === 0);
</script>
