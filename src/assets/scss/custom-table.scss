.table-bordered {
  border-collapse: collapse;
  width: 100%;

  /* Header background */
  th {
    background-color: rgb(var(--v-theme-surface-light));
  }

  /* Header vertical borders */
  th:not(:last-child) {
    border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }

  /* Data cell vertical borders */
  td:not(:last-child) {
    border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}

/* Generic utility class for freezing first 2 and last 1 columns */
.table-freeze-cols {
  border-collapse: separate;

  td {
    background-color: rgb(var(--v-theme-surface-light));
  }

  /* 2nd col */
  th:nth-child(2),
  td:nth-child(2) {
    position: sticky;
    left: var(--freeze-col-2-left, 0px);
    z-index: 3;
  }

  /* second last column */
  &--last-col-2 {

    th:nth-last-child(2),
    td:nth-last-child(2) {
      position: sticky;
      right: var(--freeze-last-col-2-right, 0px);
      z-index: 3;
      border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    }
  }

  /* last col */
  &--last-col {

    th:last-child,
    td:last-child {
      position: sticky;
      right: 0;
      z-index: 3;
      border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    }
  }
}

.po-table-freeze-cols {
  td {
    background-color: rgb(var(--v-theme-surface));
  }

  &--col-6 {
    th:nth-child(6),
    td:nth-child(6) {
      position: sticky;
      left: var(--freeze-col-5-left, 200px);
      z-index: 3;
    }
  }
}

// @todo: validate and remove below lines
.sticky-bottom-row {
  position: sticky;
  bottom: 0;
  z-index: 2;
  height: var(--v-table-header-height);
  background-color: rgb(var(--v-theme-surface-light)) !important;
  color: rgba(var(--v-theme-on-surface-light), var(--v-high-emphasis-opacity));
}

.sticky-bottom-row td {
  padding: 0 16px;
  font-weight: bold;
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}