import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useMenuRecipeStore = defineStore("menuRecipe", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const menuRecipes = ref([]);

  const getMenuRecipes = computed(() => menuRecipes.value || []);
  const setMenuRecipes  = (data) => {
    menuRecipes.value = data;
  };

  const fetchMenuRecipes = async (filters = {}) => {
    try {
      const response = await httpClient.post('menuRecipes',filters);
      setMenuRecipes(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchMenuRecipeById = async (id) => {
    try {
      const response = await httpClient.get(`menuRecipes/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createMenuRecipe = async (data) => {
    try {
      await httpClient.post("menuRecipes/create", {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Menu-Recipe created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    fetchMenuRecipes,
    fetchMenuRecipeById,
    createMenuRecipe,
    getMenuRecipes,
    setMenuRecipes,
  };
});
