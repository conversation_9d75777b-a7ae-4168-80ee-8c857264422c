import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";

export const useChargeStore = defineStore("charges", () => {
    const { showSnackbar } = useSnackbarStore();
    const charges = ref([]);

    const getCharges = computed(() => charges.value || []);
    const setCharges = (data) => {
        charges.value = data;
    };

    const fetchCharges = async () => {
        try {
            const response = await httpClient.get("charges");
            setCharges(response.data?.data);
        } catch (response) {
            showSnackbar("error", response.data.message);
            throw Error(response.data.message);
        }
    };

    const fetchChargeById = async (id) => {
        try {
            const response = await httpClient.get(`charges/${id}`);
            return response.data?.data;
        } catch ({ response }) {
            showSnackbar("error", response.data.message);
            throw Error(response.data.message);
        }
    };

    const createCharge = async (data) => {
        try {
            await httpClient.post("charges", data);
            showSnackbar("green", "Charge Created Successfully");
        } catch ({ response }) {
            showSnackbar("error", response.data.message);
            throw Error(response.data.message);
        }
    };

    const updateCharge = async (id, data) => {
        try {
            await httpClient.put(`charges/${id}`, data);
            showSnackbar("green", "Charge Updated Successfully");
        } catch ({ response }) {
            showSnackbar("error", response.data.message);
            throw Error(response.data.message);
        }
    };
        
    return {
        getCharges,
        setCharges,
        fetchCharges,
        fetchChargeById,
        createCharge,
        updateCharge
    };
});
