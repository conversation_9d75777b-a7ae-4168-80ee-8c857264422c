import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const usePurchaseRequestStore = defineStore("purchaseRequest", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const purchaseRequest = ref([]);
  const getPurchaseRequest = computed(() => purchaseRequest.value);

  const setPurchaseRequest = (data) => {
    purchaseRequest.value = data;
  };

  const fetchPurchaseRequest = async (filters) => {
    try {
      const response = await httpClient.post(`purchase-requests`, filters);
      setPurchaseRequest(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchPurchaseRequestById = async (id) => {
    try {
      const response = await httpClient.get(`purchase-requests/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };
  const createPurchaseRequest = async (data) => {
    try {
      const response = await httpClient.post("purchase-requests/create", data);
      return response.data;
      showSnackbar("success", "Purchase request created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updatePurchaseRequest = async (data) => {
    const payload = { ...data, tenantId: tenantId.value };
    try {
      await httpClient.put(`purchase-requests/${data.id}`, payload);
      showSnackbar("success", "Purchase request updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const approvePurchaseRequest = async (id) => {
    try {
      await httpClient.post(`purchase-requests/${id}/approve`);
      showSnackbar(
        "success",
        "Purchase request has been approved successfully"
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const rejectPurchaseRequest = async (id, rejectedReason) => {
    try {
      await httpClient.post(`purchase-requests/${id}/reject`, {
        rejectedReason,
      });
      showSnackbar(
        "success",
        "Purchase request has been rejected successfully"
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const closePurchaseRequest = async (id, reason) => {
    try {
      await httpClient.post(`purchase-requests/${id}/close`, { reason });
      showSnackbar("success", "Purchase Request has been closed successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const convertToPurchaseOrder = async (id) => {
    try {
      await httpClient.post(
        `purchase-requests/${id}/convert-to-purchase-order`
      );
      showSnackbar(
        "success",
        "Purchase request has been converted to purchase order successfully"
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    purchaseRequest,
    getPurchaseRequest,
    fetchPurchaseRequest,
    createPurchaseRequest,
    fetchPurchaseRequestById,
    approvePurchaseRequest,
    rejectPurchaseRequest,
    updatePurchaseRequest,
    convertToPurchaseOrder,
    closePurchaseRequest,
  };
});
