import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useProductLedgerStore = defineStore("ledger", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const productLedgers = ref([]);

  const getProductLedgers = computed(() => productLedgers.value);

  const setProductLedgers = (data) => {
    productLedgers.value = data;
  };

  const fetchProductLedgers = async () => {
    try {
      const response = await httpClient.get(`ledgers`);
      setProductLedgers(response.data || []);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Something went wrong");
      throw Error(response?.data?.message || "Request failed");
    }
  };

  const fetchProductLedgerById = async (id) => {
    try {
      const response = await httpClient.get(`ledgers/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createProductLedger = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.post("ledgers", data);
      showSnackbar("green", "Ledger Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateProductLedger = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.put(`ledgers/${data.id}`, data);
      showSnackbar("green", "Ledger Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getProductLedgers,
    fetchProductLedgers,
    fetchProductLedgerById,
    createProductLedger,
    updateProductLedger,
  };
});
