import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useAdjustmentStore = defineStore("adjustment", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const adjustments = ref([]);

  const getAdjustments = computed(() => adjustments.value || []);
  const setAdjustments = (data) => {
    adjustments.value = data;
  };

  const fetchAdjustments = async (filters = {}) => {
    try {
      const response = await httpClient.post('adjustments',filters);
      setAdjustments(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchAdjustmentById = async (id) => {
    try {
      const response = await httpClient.get(`adjustments/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createAdjustment = async (data) => {
    try {
      await httpClient.post("adjustments/create", {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Adjustment created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    fetchAdjustments,
    fetchAdjustmentById,
    createAdjustment,
    getAdjustments,
    setAdjustments,
  };
});
