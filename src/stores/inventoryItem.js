import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useInventoryItemStore = defineStore("inventoryItem", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const InventoryItems = ref([]);

  const getInventoryItems = computed(() => InventoryItems.value || []);

  const setInventoryItems = (data) => {
    InventoryItems.value = data;
  };

  const fetchInventoryItems = async () => {
    try {
      const response = await httpClient.get(`inventory-items`);
      setInventoryItems(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchInventoryItemById = async (id) => {
    try {
      const response = await httpClient.get(`inventory-items/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createInventoryItem = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const response = await httpClient.post("inventory-items", data);
      showSnackbar("green", "Inventory Item Created Successfully");
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateInventoryItem = async (data) => {
    try {
      await httpClient.put(`inventory-items/${data.id}`, data);
      showSnackbar("green", "Inventory Item Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchItemDetails = async (data, contract = false) => {
    try {
      const response = await httpClient.post(
        `inventory-items/${data.id}/details?contract=${contract}`,
        data
      );
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const getTransferDetails = async (id) => {
    try {
      const response = await httpClient.get(`inventory-items/transferDetails/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getInventoryItems,
    fetchInventoryItems,
    fetchInventoryItemById,
    createInventoryItem,
    updateInventoryItem,
    fetchItemDetails,
    getTransferDetails
    // exportItems
  };
});
