import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useReportGroupStore = defineStore("reportGroup", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const reportGroups = ref([]);

  const getReportGroups = computed(() => reportGroups.value);

  const setReportGroups = (data) => {
    reportGroups.value = data;
  };

  const fetchReportGroups = async () => {
    try {
      const response = await httpClient.get(`report-groups`);
      setReportGroups(response.data || []);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Something went wrong");
      throw Error(response?.data?.message || "Request failed");
    }
  };

  const fetchReportGroupById = async (id) => {
    try {
      const response = await httpClient.get(`report-groups/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createReportGroup = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.post("report-groups", data);
      showSnackbar("green", "Report Group Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateReportGroup = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await httpClient.put(`report-groups/${data.id}`, data);
      showSnackbar("green", "Report Group Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    getReportGroups,
    fetchReportGroups,
    fetchReportGroupById,
    createReportGroup,
    updateReportGroup,
  };
});
