import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useGrnStore = defineStore("grnList", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();

  const grnList = ref([]);
  const getGrnList = computed(() => grnList.value);

  const setGrnList = (data) => {
    grnList.value = data;
  };

  const fetchGrnList = async (filters) => {
    try {
      const response = await httpClient.post("purchases/grns", filters);
      setGrnList(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchGrnById = async (id) => {
    try {
      const response = await httpClient.get(`purchases/grns/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createGrn = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const res = await httpClient.post("purchases/create-grn", data);
      //showSnackbar("success", "GRN created successfully");
      return res.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const getLastItemPrice = async (data) => {
    try {
      const response = await httpClient.post(
        `purchases/grns/last-purchase-prices`,
        data
      );
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const deleteGRN = async (id, reason) => {
    try {
      await httpClient.post(`purchases/grns/${id}/delete`, { reason });
      showSnackbar("green", "GRN deleted successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const returnGRN = async (id, payload) => {
    try {
      await httpClient.post(`purchases/grns/${id}/return-vendor`, payload);
      showSnackbar("green", "GRN Returned to Vendor successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateGRN = async (data) => {
    try {
      await httpClient.post(`purchases/grns/${data.id}`, data);
      showSnackbar("green", "GRN Updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    grnList,
    getGrnList,
    fetchGrnList,
    createGrn,
    fetchGrnById,
    getLastItemPrice,
    deleteGRN,
    returnGRN,
    updateGRN,
  };
});
