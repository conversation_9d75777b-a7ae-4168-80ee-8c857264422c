import { defineStore } from "pinia";
import { ref } from "vue";
import httpClient from "@/plugin/Axios";
import { getUserDetails } from "@/helpers/auth";

export const useMasterStore = defineStore("master", () => {
  /** ---------------- Master Data ---------------- */
  const locations = ref([]);
  const inventoryLocations = ref([]);
  const categories = ref([]);
  const subCategories = ref([]);
  const inventoryItems = ref([]);
  const vendors = ref([]);
  const taxes = ref([]);
  const charges = ref([]);
  const tags = ref([]);
  const houseUnits = ref([]);
  const ledgers = ref([]);

  /** ---------------- Loading / Fetch State ---------------- */
  const loading = ref(false);
  const dataFetched = ref(null);
  const inventoryDataFetched = ref(null);
  let fetchPromise = null;
  let inventoryFetchPromise = null;

  const _reset = () => {
    locations.value = [];
    inventoryLocations.value = [];
    categories.value = [];
    subCategories.value = [];
    vendors.value = [];
    taxes.value = [];
    charges.value = [];
    tags.value = [];
    houseUnits.value = [];
    ledgers.value = [];
    dataFetched.value = null;
  };
  const _resetItems = () => {
    inventoryItems.value = [];
    inventoryDataFetched.value = null;
  };
  const _getTenantId = () => getUserDetails()?.tenantId;
  const _ensureData = (oldTenantId) => {
    return oldTenantId && oldTenantId === _getTenantId();
  };

  // const userStore = useUserStore();

  /** ---------------- Fetch Master Data ---------------- */
  async function fetchMasterData() {
    // if (_ensureData(dataFetched.value)) return Promise.resolve();
    if (fetchPromise) return fetchPromise;
    _reset();

    loading.value = true;
    fetchPromise = httpClient
      .get("/master")
      .then((res) => {
        const data = res.data || {};
        locations.value = data.locations || [];
        inventoryLocations.value = data.inventoryLocations || [];
        categories.value = data.categories || [];
        subCategories.value = data.subCategories || [];
        vendors.value = data.vendors || [];
        taxes.value = data.taxes || [];
        charges.value = data.charges || [];
        tags.value = data.tags || [];
        houseUnits.value = data.houseUnits || [];
        ledgers.value = data.productLedgers || [];
        dataFetched.value = _getTenantId();
      })
      .catch((err) => console.error("Failed to fetch master data:", err))
      .finally(() => {
        loading.value = false;
        fetchPromise = null; // reset once done
      });

    return fetchPromise;
  }

  async function fetchMasterInventoryItemData() {
    // if (_ensureData(inventoryDataFetched.value)) return Promise.resolve();
    if (inventoryFetchPromise) return inventoryFetchPromise;
    _resetItems();

    loading.value = true;
    inventoryFetchPromise = httpClient
      .get("/master/inventory-items")
      .then((res) => {
        inventoryItems.value = res.data || [];
        inventoryDataFetched.value = _getTenantId();
      })
      .catch((err) =>
        console.error("Failed to fetch master Inventory Item data:", err)
      )
      .finally(() => {
        loading.value = false;
        inventoryFetchPromise = null; // reset once done
      });
  }

  async function ensureData() {
    if (!_ensureData(dataFetched.value)) {
      fetchMasterData();
    }
  }

  async function ensureInventoryItemData() {
    if (!_ensureData(inventoryDataFetched.value)) {
      await fetchMasterInventoryItemData();
    }
  }

  /** ---------------- Apply Filters ----------------
  /**
   * Ensures data is loaded before filtering.
   * Supports filter values as exact match or function.
   * If no filters are provided or all filter values are empty, the original data is returned.
   * If any valid filters exist, the data is filtered according to the filter values.
   * A filter value is considered "empty" if it is null, undefined, an empty string, or an empty array.
   * The filtering is done by using the Array.prototype.filter() method.
   * If a filter value is a function, it is called with the item[key] as an argument and the result of the function is used to filter the item.
   * If a filter value is an array, it is checked if the item[key] is included in the array.
   * If a filter value is not an array or a function, it is checked if the item[key] is equal to the filter value.
   */
  function applyFilters(data, filters = {}) {
    // Skip filtering if data is not an array or is empty
    if (!Array.isArray(data) || data.length === 0) {
      return data;
    }

    // Helper to check if a filter value is considered "empty"
    // Helper to check if a filter value is considered "empty"
    const isEmptyFilterValue = (val) => {
      // Internal helper: check primitive or array emptiness
      const validate = (v) =>
        v === null ||
        v === undefined ||
        v === "" ||
        (Array.isArray(v) && v.length === 0);
      // Primitive or array
      if (validate(val)) return true;

      // If it's an object, check $in/$not
      if (typeof val === "object" && !Array.isArray(val)) {
        const hasInclude = val.$in !== undefined && !validate(val.$in);
        const hasNot = val.$not !== undefined && !validate(val.$not);
        // Consider empty if neither $in nor $not is valid
        if (!hasInclude && !hasNot) return true;
      }

      return false;
    };

    // Determine if any valid filters exist
    const hasFilter =
      filters &&
      Object.keys(filters).some((key) => !isEmptyFilterValue(filters[key]));
    if (!hasFilter) {
      return data;
    }

    // Apply filters
    return data.filter((item) => {
      // Iterate over each filter key
      return Object.keys(filters).every((key) => {
        const filterVal = filters[key];
        const itemVal = item[key];

        // SPECIAL CASE: vendor filter + item.allVendors === true
        if (key === "vendors") {
          // if item has allVendors = true, skip vendor filtering entirely
          if (item.allVendors === true) {
            return true;
          }
        }

        if (isEmptyFilterValue(filterVal)) {
          return true;
        }

        if (filterVal?.$not) {
          // Check '$not' first
          const isNotValid = Array.isArray(filterVal.$not)
            ? filterVal.$not.includes(itemVal)
            : itemVal === filterVal.$not;

          // If no $in, just return the '$not' result
          if (!filterVal?.$in) {
            return !isNotValid;
          }

          // If the item is in '$not', exclude it
          if (isNotValid) {
            return false;
          }
        }

        // Helper to validate value
        const validate = (val) => {
          if (typeof val === "function") return val(itemVal); // fix: use argument val
          if (Array.isArray(val) && val.length > 0) {
            if (Array.isArray(itemVal)) {
              return val.some((v) => itemVal.includes(v));
            }
            return val.includes(itemVal);
          }
          return itemVal === val;
        };

        // Check $in if present
        if (filterVal?.$in) {
          return validate(filterVal.$in);
        }

        // Fallback validation
        return validate(filterVal);
      });
    });
  }

  /**
   * Gets the locations, applying filters and user auth restrictions.
   * If noAuth is true, the user auth restrictions are skipped.
   * The user auth restrictions are:
   *   - If the user is an admin, all locations are returned.
   *   - If the user has all locations, all locations are returned.
   *   - Otherwise, only the locations that the user has access to are returned.
   * @param {Object} filters - The filters to apply to the locations.
   * @param {Boolean} noAuth - If true, the user auth restrictions are skipped.
   * @returns {Array} The filtered locations.
   */
  function getLocations(filters = {}, noAuth = false) {
    // Apply the initial filters
    let data = applyFilters(locations.value, filters);
    if (noAuth) {
      // If noAuth is true, skip the user auth restrictions
      return data;
    }

    // Apply the user auth restrictions
    const user = getUserDetails();
    if (user.isAdmin) {
      // If the user is an admin, all locations are returned
      return data;
    }

    if (user.allLocations) {
      // If the user has all locations, all locations are returned
      return data;
    }

    // Otherwise, only the locations that the user has access to are returned
    return data.filter((loc) => user.locationIds?.includes(loc.id));
  }

  /**
   * Gets the work areas, applying filters and user auth restrictions.
   * First, the filters are applied to the work areas.
   * Then, if noAuth is false, the user auth restrictions are applied.
   * The user auth restrictions are:
   *   - If the user is an admin, all work areas are returned.
   *   - If the user has all locations, all work areas are returned.
   *   - If the user has all inventory locations, all work areas are returned.
   *   - Otherwise, only the work areas that the user has access to are returned.
   * @param {Object} filters - The filters to apply to the work areas.
   * @param {Boolean} noAuth - If true, the user auth restrictions are skipped.
   * @returns {Array} The filtered work areas.
   */
  function getInventoryLocations(filters = {}, noAuth = false) {
    // First, apply the filters to the work areas
    let data = applyFilters(inventoryLocations.value, filters);

    // If noAuth is true, skip the user auth restrictions
    if (noAuth) {
      return data;
    }

    // Apply the user auth restrictions
    const user = getUserDetails();
    // If the user is an admin, all work areas are returned
    if (user.isAdmin) {
      return data;
    }

    // If the user has all locations, all work areas are returned
    if (user.allLocations) {
      return data;
    }

    // If the user has all inventory locations, all work areas are returned
    if (user.allInventoryLocations) {
      return data;
    }

    // Otherwise, only the work areas that the user has access to are returned
    return data.filter((loc) => user.inventoryLocationIds?.includes(loc.id));
  }

  /** ---------------- Get Categories ---------------- */
  function getCategories(filters = {}) {
    return applyFilters(categories.value, filters);
  }

  /** ---------------- Get Subcategories ---------------- */
  function getSubCategories(filters = {}) {
    return applyFilters(subCategories.value, filters);
  }

  /** ---------------- Get Menu Items ---------------- */
  function getInventoryItems(filters = {}) {
    return applyFilters(inventoryItems.value, filters);
  }

  function getVendors(filters = {}) {
    return applyFilters(vendors.value, filters);
  }

  function getTaxes(filters = {}) {
    return applyFilters(taxes.value, filters);
  }

  function getCharges(filters = {}) {
    return applyFilters(charges.value, filters);
  }

  function getTags(filters = {}) {
    return applyFilters(tags.value, filters);
  }

  function getHouseUnits(filters = {}) {
    return applyFilters(houseUnits.value, filters);
  }

  function getLedgers(filters = {}) {
    return applyFilters(ledgers.value, filters);
  }

  /** ---------------- Return Store ---------------- */
  return {
    locations,
    inventoryLocations,
    categories,
    subCategories,
    inventoryItems,
    loading,
    fetchMasterData,
    ensureData,
    applyFilters,
    getLocations,
    getInventoryLocations,
    getCategories,
    getSubCategories,
    getInventoryItems,
    getVendors,
    getTaxes,
    getCharges,
    getTags,
    getHouseUnits,
    getLedgers,
    ensureInventoryItemData,
  };
});
