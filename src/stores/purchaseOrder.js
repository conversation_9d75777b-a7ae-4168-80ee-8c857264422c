import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const usePurchaseOrderStore = defineStore("purchaseOrder", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const purchaseOrder = ref([]);
  const getPurchaseOrder = computed(() => purchaseOrder.value);

  const setPurchaseOrder = (data) => {
    purchaseOrder.value = data;
  };

  const fetchPurchaseOrder = async (filters) => {
    try {
      const response = await httpClient.post(`purchase-orders`, filters);
      setPurchaseOrder(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchPurchaseOrderById = async (id) => {
    try {
      const response = await httpClient.get(`purchase-orders/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createPurchaseOrder = async (data) => {
    try {
      await httpClient.post("purchase-orders/create", data);
      showSnackbar("success", "Purchase order created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const approvePurchaseOrder = async (id) => {
    try {
      await httpClient.post(`purchase-orders/${id}/approve`);
      showSnackbar("success", "Purchase order has been approved successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const rejectPurchaseOrder = async (id, rejectedReason) => {
    try {
      await httpClient.post(`purchase-orders/${id}/reject`, { rejectedReason });
      showSnackbar("success", "Purchase order has been rejected successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const closePurchaseOrder = async (id, reason) => {
    try {
      await httpClient.post(`purchase-orders/${id}/close`, { reason });
      showSnackbar("success", "Purchase Order has been closed successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updatePurchaseOrder = async (data) => {
    const payload = { ...data, tenantId: tenantId.value };
    try {
      await httpClient.put(`purchase-orders/${data.id}`, payload);
      showSnackbar("success", "Purchase order updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    purchaseOrder,
    getPurchaseOrder,
    fetchPurchaseOrder,
    fetchPurchaseOrderById,
    createPurchaseOrder,
    approvePurchaseOrder,
    rejectPurchaseOrder,
    updatePurchaseOrder,
    closePurchaseOrder,
  };
});
