{"name": "inventory-ui", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "staging": "vite build --mode staging", "prod": "vite build --mode production", "preview": "vite preview", "test:unit": "vitest", "test:coverage": "vitest run --coverage", "deploy:staging": "firebase deploy --only hosting"}, "dependencies": {"@mdi/font": "^7.4.47", "axios": "^1.10.0", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "papaparse": "^5.5.3", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-currency-input": "^3.2.1", "vue-draggable-next": "^2.2.1", "vue-router": "^4.5.0", "vuetify": "^3.10.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^3.1.3", "@vue/test-utils": "^2.4.6", "jsdom": "^26.0.0", "sass-embedded": "^1.80.6", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.5"}}